/**
 * Schema.org structured data for yoga retreats
 * Optimized for wellness/travel industry
 */

export function generateRetreatSchema(retreat) {
  return {
    "@context": "https://schema.org",
    "@type": "Event",
    "name": retreat.title,
    "description": retreat.description,
    "startDate": retreat.startDate,
    "endDate": retreat.endDate,
    "eventStatus": "https://schema.org/EventScheduled",
    "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
    "location": {
      "@type": "Place",
      "name": retreat.location.name,
      "address": {
        "@type": "PostalAddress",
        "addressCountry": retreat.location.country,
        "addressRegion": retreat.location.region,
        "addressLocality": retreat.location.city
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": retreat.location.lat,
        "longitude": retreat.location.lng
      }
    },
    "image": retreat.images.map(img => img.url),
    "organizer": {
      "@type": "Person",
      "name": "<PERSON>",
      "url": "https://bakasana.studio/juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor",
      "sameAs": [
        "https://www.instagram.com/bakasana.studio",
        "https://www.facebook.com/bakasana.studio"
      ]
    },
    "offers": {
      "@type": "Offer",
      "price": retreat.price,
      "priceCurrency": "PLN",
      "availability": "https://schema.org/InStock",
      "validFrom": new Date().toISOString(),
      "url": `https://bakasana.studio${retreat.slug}`
    },
    "performer": {
      "@type": "Person",
      "name": "Julia Jakubowicz",
      "description": "Certyfikowana instruktorka jogi z 8-letnim doświadczeniem"
    },
    "audience": {
      "@type": "Audience",
      "audienceType": "Adults interested in yoga and wellness"
    },
    "category": "Yoga Retreat",
    "keywords": [
      "yoga retreat",
      "wellness",
      "meditation",
      "spiritual journey",
      retreat.location.country.toLowerCase(),
      "transformacja",
      "joga"
    ].join(", ")
  };
}

export function generateOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Bakasana Studio",
    "alternateName": "Bakasana - Transformacyjne Podróże",
    "url": "https://bakasana.studio",
    "logo": "https://bakasana.studio/images/logo.png",
    "description": "Ekskluzywne retreaty jogi i transformacyjne podróże po Azji z certyfikowaną instruktorką Julią Jakubowicz.",
    "foundingDate": "2020",
    "founder": {
      "@type": "Person",
      "name": "Julia Jakubowicz"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+***********",
      "contactType": "customer service",
      "availableLanguage": ["Polish", "English"],
      "serviceType": "WhatsApp Premium Support"
    },
    "sameAs": [
      "https://www.instagram.com/bakasana.studio",
      "https://www.facebook.com/bakasana.studio"
    ],
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "PL",
      "addressRegion": "Mazowieckie",
      "addressLocality": "Warszawa"
    },
    "areaServed": {
      "@type": "Country",
      "name": "Poland"
    },
    "serviceType": [
      "Yoga Retreats",
      "Wellness Travel",
      "Meditation Workshops",
      "Spiritual Journeys"
    ]
  };
}

export function generateWebsiteSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Bakasana Studio",
    "url": "https://bakasana.studio",
    "description": "Transformacyjne retreaty jogi i podróże wellness po Azji",
    "publisher": {
      "@type": "Organization",
      "name": "Bakasana Studio"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://bakasana.studio/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };
}

export function generateBreadcrumbSchema(breadcrumbs) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": `https://bakasana.studio${crumb.href}`
    }))
  };
}