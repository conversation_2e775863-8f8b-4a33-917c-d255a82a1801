'use client';

import { useEffect, useState } from 'react';

/**
 * 🔍 FEATURE DETECTION COMPONENT
 * Detects browser capabilities and applies appropriate fallbacks
 */

export default function FeatureDetection() {
  const [features, setFeatures] = useState({
    cssGrid: false,
    flexbox: false,
    backdropFilter: false,
    containerQueries: false,
    webp: false,
    avif: false
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const detectFeatures = () => {
      const detected = {
        // CSS Grid support
        cssGrid: CSS.supports('display', 'grid'),
        
        // Flexbox support
        flexbox: CSS.supports('display', 'flex'),
        
        // Backdrop filter support
        backdropFilter: CSS.supports('backdrop-filter', 'blur(10px)') || 
                       CSS.supports('-webkit-backdrop-filter', 'blur(10px)'),
        
        // Container queries support
        containerQueries: CSS.supports('container-type', 'inline-size'),
        
        // WebP support
        webp: false,
        
        // AVIF support
        avif: false
      };

      // Test WebP support
      const webpTest = new Image();
      webpTest.onload = webpTest.onerror = () => {
        detected.webp = webpTest.height === 2;
        setFeatures(prev => ({ ...prev, webp: detected.webp }));
        applyFeatureClasses({ ...detected, webp: detected.webp });
      };
      webpTest.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';

      // Test AVIF support
      const avifTest = new Image();
      avifTest.onload = avifTest.onerror = () => {
        detected.avif = avifTest.height === 2;
        setFeatures(prev => ({ ...prev, avif: detected.avif }));
        applyFeatureClasses({ ...detected, avif: detected.avif });
      };
      avifTest.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';

      setFeatures(detected);
      applyFeatureClasses(detected);
    };

    const applyFeatureClasses = (detectedFeatures) => {
      const html = document.documentElement;
      
      // Apply feature classes to HTML element
      Object.entries(detectedFeatures).forEach(([feature, supported]) => {
        if (supported) {
          html.classList.add(`supports-${feature}`);
          html.classList.remove(`no-${feature}`);
        } else {
          html.classList.add(`no-${feature}`);
          html.classList.remove(`supports-${feature}`);
        }
      });

      // Apply browser-specific classes
      const userAgent = navigator.userAgent;
      if (userAgent.includes('Chrome') && !userAgent.includes('Edge')) {
        html.classList.add('browser-chrome');
      } else if (userAgent.includes('Firefox')) {
        html.classList.add('browser-firefox');
      } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        html.classList.add('browser-safari');
      } else if (userAgent.includes('Edge')) {
        html.classList.add('browser-edge');
      }

      // Apply device classes
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
      
      if (isMobile && !isTablet) {
        html.classList.add('device-mobile');
      } else if (isTablet) {
        html.classList.add('device-tablet');
      } else {
        html.classList.add('device-desktop');
      }

      // Feature detection complete
    };

    detectFeatures();
  }, []);

  return null; // This component doesn't render anything
}

// CSS Fallbacks for older browsers
export const FeatureDetectionStyles = () => (
  <style jsx global>{`
    /* CSS Grid Fallbacks */
    .no-cssGrid .destinations-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 60px;
    }
    
    .no-cssGrid .destinations-grid > * {
      flex: 1 1 480px;
      min-width: 480px;
    }

    .no-cssGrid .magazine-grid {
      display: block;
    }

    .no-cssGrid .magazine-grid > * {
      margin-bottom: 40px;
    }

    /* Flexbox Fallbacks */
    .no-flexbox .flex {
      display: block;
    }

    .no-flexbox .flex > * {
      display: inline-block;
      vertical-align: top;
    }

    /* Backdrop Filter Fallbacks */
    .no-backdropFilter .backdrop-blur {
      background: rgba(255, 255, 255, 0.9);
    }

    .no-backdropFilter .glass-effect {
      background: rgba(255, 255, 255, 0.95);
    }

    /* WebP/AVIF Fallbacks */
    .no-webp .webp-image {
      background-image: url('fallback.jpg') !important;
    }

    .no-avif .avif-image {
      background-image: url('fallback.webp') !important;
    }

    /* Browser-specific fixes */
    .browser-safari .sticky {
      position: -webkit-sticky;
      position: sticky;
    }

    .browser-firefox .backdrop-blur {
      /* Firefox doesn't support backdrop-filter well */
      background: rgba(255, 255, 255, 0.95);
    }

    /* Mobile-specific adjustments */
    .device-mobile .destinations-grid {
      grid-template-columns: 1fr;
      gap: 40px;
    }

    .device-mobile .magazine-grid {
      grid-template-columns: 1fr;
      gap: 30px;
    }

    /* Tablet-specific adjustments */
    .device-tablet .destinations-grid {
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 50px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      .supports-cssGrid .destinations-grid {
        border: 2px solid currentColor;
      }
      
      .card {
        border: 2px solid currentColor !important;
      }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }

    /* Print styles */
    @media print {
      .no-cssGrid .destinations-grid,
      .supports-cssGrid .destinations-grid {
        display: block !important;
      }
      
      .destinations-grid > * {
        page-break-inside: avoid;
        margin-bottom: 20px;
      }
    }
  `}</style>
);
