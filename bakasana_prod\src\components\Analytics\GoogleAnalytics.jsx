/**
 * 📊 BAKASANA - UNIFIED GOOGLE ANALYTICS COMPONENT
 * 
 * Skonsolidowany komponent Google Analytics z:
 * - Enhanced ecommerce tracking
 * - Custom events dla yoga retreat business
 * - Proper error handling
 * - Performance optimization
 */

'use client';

import Script from 'next/script';
import { useEffect } from 'react';

const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

// Analytics event tracking functions
export const analytics = {
  // Page view tracking
  pageView: (url) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', GA_TRACKING_ID, {
        page_path: url,
      });
    }
  },

  // Retreat specific events
  trackRetreatInterest: (retreatName, price) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'retreat_interest', {
        event_category: 'Retreats',
        event_label: retreatName,
        value: price,
        currency: 'PLN'
      });
    }
  },

  trackRetreatBooking: (retreatName, price) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'purchase', {
        transaction_id: Date.now().toString(),
        value: price,
        currency: 'PLN',
        items: [{
          item_id: retreatName.toLowerCase().replace(/\s+/g, '-'),
          item_name: retreatName,
          category: 'Retreat',
          quantity: 1,
          price: price
        }]
      });
    }
  },

  // Form tracking
  trackFormStart: (formType) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'form_start', {
        event_category: 'Forms',
        event_label: formType
      });
    }
  },

  trackFormComplete: (formType) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'form_complete', {
        event_category: 'Forms',
        event_label: formType
      });
    }
  },

  // Engagement tracking
  trackVideoPlay: (videoTitle) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'video_play', {
        event_category: 'Video',
        event_label: videoTitle
      });
    }
  },

  trackScrollDepth: (percentage) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'scroll', {
        event_category: 'Engagement',
        event_label: `${percentage}%`
      });
    }
  },

  // Custom events
  trackCustomEvent: (eventName, parameters = {}) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, parameters);
    }
  }
};

export default function GoogleAnalytics() {
  if (!GA_TRACKING_ID) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Google Analytics Measurement ID (NEXT_PUBLIC_GA_MEASUREMENT_ID) is not set.');
    }
    return null;
  }

  useEffect(() => {
    // Make analytics functions globally available
    if (typeof window !== 'undefined') {
      window.analytics = analytics;
    }
  }, []);

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
        onLoad={() => {
          if (process.env.NODE_ENV === 'development') {
            console.log('Google Analytics loaded successfully');
          }
        }}
        onError={(e) => {
          console.error('Error loading Google Analytics:', e);
        }}
      />
      <Script
        id="google-analytics-config"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            gtag('config', '${GA_TRACKING_ID}', {
              page_path: window.location.pathname,
              send_page_view: true,
              allow_google_signals: true,
              allow_ad_personalization_signals: true,
              cookie_flags: 'SameSite=None;Secure',
              currency: 'PLN',
              country: 'PL',
              custom_map: {
                'custom_parameter_1': 'user_type',
                'custom_parameter_2': 'page_category',
                'custom_parameter_3': 'engagement_level'
              }
            });

            // Make gtag globally available
            window.gtag = gtag;
          `,
        }}
      />
    </>
  );
}