/**
 * OLD MONEY STYLES - Elegant & Timeless
 * Sophisticated styling for luxury yoga retreats
 */

/* =============================================
   OLD MONEY ANIMATIONS - Refined & Subtle
   ============================================= */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes elegantFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* =============================================
   OLD MONEY UTILITY CLASSES
   ============================================= */

.old-money-fade-in {
  animation: fadeInUp 0.8s var(--ease-gentle) forwards;
}

.old-money-scale-in {
  animation: scaleIn 0.6s var(--ease-gentle) forwards;
}

.old-money-float {
  animation: elegantFloat 4s ease-in-out infinite;
}

/* =============================================
   OLD MONEY TYPOGRAPHY ENHANCEMENTS
   ============================================= */

.old-money-heading {
  font-family: var(--font-primary);
  font-weight: 300;
  line-height: var(--leading-tight);
  letter-spacing: 0.02em;
  color: var(--charcoal);
}

.old-money-body {
  font-family: var(--font-secondary);
  font-weight: 300;
  line-height: var(--leading-normal);
  color: var(--charcoal-light);
}

.old-money-accent {
  color: var(--enterprise-brown);
  position: relative;
}

.old-money-accent::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, var(--enterprise-brown), transparent);
  opacity: 0.6;
}

/* =============================================
   OLD MONEY CARD SYSTEM
   ============================================= */

.old-money-card {
  background: var(--sanctuary);
  border: 1px solid var(--border-subtle);
  box-shadow: var(--shadow-subtle);
  transition: all var(--duration-normal) var(--ease-gentle);
}

.old-money-card:hover {
  border-color: var(--border-light);
  box-shadow: var(--shadow-elegant);
  transform: scale(1.02);
}

/* =============================================
   OLD MONEY BUTTON SYSTEM - Ghost Style
   ============================================= */

.old-money-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  border: 1px solid var(--enterprise-brown);
  background: transparent;
  color: var(--enterprise-brown);
  font-family: var(--font-secondary);
  font-weight: 400;
  letter-spacing: 0.05em;
  transition: all var(--duration-normal) var(--ease-gentle);
  cursor: pointer;
}

.old-money-button:hover {
  background: var(--enterprise-brown);
  color: var(--sanctuary);
  transform: scale(1.02);
  box-shadow: var(--shadow-elegant);
}

.old-money-button:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

/* =============================================
   OLD MONEY DIVIDERS & ORNAMENTS
   ============================================= */

.old-money-divider {
  position: relative;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-light), transparent);
  margin: 4rem 0;
}

.old-money-divider::before {
  content: '◊';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--sanctuary);
  color: var(--enterprise-brown);
  padding: 0 1rem;
  font-size: 0.875rem;
  opacity: 0.7;
}

/* =============================================
   OLD MONEY HERO ENHANCEMENTS
   ============================================= */

.old-money-hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--sanctuary);
}

.old-money-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, transparent 0%, var(--sanctuary) 100%);
  pointer-events: none;
}

/* =============================================
   OLD MONEY SCROLL ANIMATIONS
   ============================================= */

.old-money-scroll-fade {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s var(--ease-gentle);
}

.old-money-scroll-fade.visible {
  opacity: 1;
  transform: translateY(0);
}

/* =============================================
   OLD MONEY RESPONSIVE REFINEMENTS
   ============================================= */

@media (max-width: 768px) {
  .old-money-heading {
    font-size: clamp(2rem, 8vw, 3rem);
    line-height: 1.2;
  }
  
  .old-money-body {
    font-size: 1rem;
    line-height: 1.7;
  }
  
  .old-money-card {
    margin: 1rem 0;
  }
  
  .old-money-divider {
    margin: 2rem 0;
  }
}

/* =============================================
   OLD MONEY ACCESSIBILITY ENHANCEMENTS
   ============================================= */

@media (prefers-reduced-motion: reduce) {
  .old-money-fade-in,
  .old-money-scale-in,
  .old-money-float,
  .old-money-scroll-fade {
    animation: none;
    transition: none;
  }
  
  .old-money-card:hover,
  .old-money-button:hover {
    transform: none;
  }
}

/* =============================================
   OLD MONEY FOCUS STYLES
   ============================================= */

.old-money-focus:focus {
  outline: 2px solid var(--enterprise-brown);
  outline-offset: 2px;
  border-radius: 2px;
}

/* =============================================
   OLD MONEY SELECTION STYLES
   ============================================= */

::selection {
  background: var(--enterprise-brown);
  color: var(--sanctuary);
}

::-moz-selection {
  background: var(--enterprise-brown);
  color: var(--sanctuary);
}