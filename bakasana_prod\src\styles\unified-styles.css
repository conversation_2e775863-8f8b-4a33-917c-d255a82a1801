/* =============================================
   🏛️ BAKASANA - UNIFIED STYLES SYSTEM
   Production-optimized CSS consolidation
   ============================================= */

/* Import unified design tokens - SINGLE SOURCE OF TRUTH */
@import url('./design-tokens.css');

/* ===== CRITICAL ABOVE-THE-FOLD STYLES ===== */

/* Base styles */
body {
  font-family: var(--font-secondary);
  background: var(--sanctuary);
  color: var(--charcoal);
  margin: 0;
  padding: 0;
  line-height: var(--leading-normal);
}

/* Navigation - Critical for first paint */
.navigation {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100;
  background: transparent;
  transition: all var(--duration-normal) var(--ease-smooth);
}

/* Hero section - Above the fold */
.hero {
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: linear-gradient(135deg, var(--sanctuary) 0%, #F9F7F2 50%, #F5F3EF 100%);
}

/* ===== ACCESSIBILITY - SKIP LINKS ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 500;
  border-radius: 0;
  z-index: 1000;
  transition: top 300ms ease;
}

.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== UNIFIED ANIMATIONS ===== */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

/* ===== BREATHING INTERACTIONS ===== */
.btn-breathing {
  background: linear-gradient(135deg, var(--sanctuary) 0%, var(--whisper) 100%);
  border: 1px solid var(--stone-light);
  color: var(--charcoal);
  transition: all var(--duration-normal) var(--ease-smooth);
  position: relative;
  overflow: hidden;
}

.btn-breathing::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--duration-slow) var(--ease-smooth);
}

.btn-breathing:hover::before {
  left: 100%;
}

.btn-breathing:hover {
  background: linear-gradient(135deg, var(--whisper) 0%, var(--silk) 100%);
  border-color: var(--enterprise-brown);
  color: var(--enterprise-brown);
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

/* ===== WARM UNDERLINE EFFECT ===== */
.warm-underline {
  position: relative;
  text-decoration: none;
}

.warm-underline::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--rose-gold), var(--temple-gold));
  transition: width var(--duration-normal) var(--ease-smooth);
}

.warm-underline:hover::after {
  width: 100%;
}

/* ===== WHATSAPP BUTTON - CRITICAL ===== */
.whatsapp-elegant {
  background-color: var(--enterprise-brown);
  border-radius: 50%;
  transition: all var(--duration-normal) var(--ease-smooth);
}

.whatsapp-elegant:hover {
  background-color: rgba(var(--enterprise-brown-rgb), 0.9);
  transform: scale(1.05);
}

.whatsapp-float {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  box-shadow: var(--shadow-elevated);
  z-index: 50;
  margin-bottom: env(safe-area-inset-bottom, 0);
}

@media (max-width: 768px) {
  .whatsapp-float {
    bottom: 1.5rem;
    right: 1.5rem;
  }
}

.whatsapp-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

/* ===== RESPONSIVE UTILITIES ===== */
.container-bakasana {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--space-container);
}

@media (max-width: 768px) {
  .container-bakasana {
    padding: 0 var(--space-md);
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* ===== PRINT STYLES ===== */
@media print {
  .navigation,
  .whatsapp-float,
  .skip-link {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}