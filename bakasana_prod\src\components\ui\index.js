/**
 * 🎨 BAKASANA - UNIFIED UI SYSTEM
 * Elegancja Old Money + Ciepły minimalizm + Organiczne elementy
 */

// === UNIFIED COMPONENTS - NEW DESIGN SYSTEM ===
export { default as UnifiedButton, CTAButton, SecondaryButton, GhostButton, LinkButton } from './UnifiedButton';
export { default as UnifiedCard, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, RetreatCard, TestimonialCard, ServiceCard, MinimalCard } from './UnifiedCard';
export { UnifiedInput, UnifiedTextarea, UnifiedLabel, UnifiedSelect, UnifiedCheckbox, InputError, InputHelper, FieldGroup } from './UnifiedInput';
export { HeroTitle, SectionTitle, CardTitle as TypographyCardTitle, SubTitle, BodyText, LeadText, SmallText, Quote, Badge, NavLink, FormLabel, StatNumber, StatLabel, Divider, OrganicAccent } from './UnifiedTypography';
export { default as UnifiedFeedback, SuccessMessage, ErrorMessage, WarningMessage, InfoMessage, LoadingMessage, UnifiedToast, useUnifiedToast } from './UnifiedFeedback';

// === CONSOLIDATED COMPONENTS ===
export { default as OptimizedImage, HeroImage, GalleryImage, ProfileImage, BlogImage, ThumbnailImage } from './OptimizedImage';
export { default as Hero, BakasanaHero, MinimalHero, ProfessionalHero, ElegantHero, SimpleHero, SpectacularHero } from './Hero';
export { default as Breadcrumbs, MinimalBreadcrumbs, ElegantBreadcrumbs, ProfessionalBreadcrumbs, SectionBreadcrumbs } from './Breadcrumbs';

// === LEGACY COMPONENTS ===
export { default as EnhancedButton } from './EnhancedButton';
export { default as GlassCard } from './GlassCard';
export { default as ParallaxSection, MultiLayerParallax, ParallaxText } from './ParallaxSection';


// Animation hooks are available directly from the hooks directory
// Import them directly: import { useScrollReveal } from '@/hooks/useAdvancedAnimations';

// Re-export accessibility
export { default as AccessibilityProvider, useAccessibility } from '../accessibility/AccessibilityProvider';

// Re-export utilities
export { cn, debounce, throttle, formatPrice, validateEmail, generateId, clamp, lerp } from '../../lib/utils';