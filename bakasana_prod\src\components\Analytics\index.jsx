/**
 * 📊 BAKASANA - UNIFIED ANALYTICS SYSTEM
 * 
 * Skonsolidowany system analytics - 2 główne komponenty:
 * - GoogleAnalytics: Podstawowy GA4 tracking
 * - AnalyticsProvider: Provider z wszystkimi funkcjami
 */

'use client';

import { Analytics as VercelAnalytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import GoogleAnalytics from './GoogleAnalytics';
import { useEffect } from 'react';

export function AnalyticsProvider({ children }) {
  useEffect(() => {
    // Track scroll depth
    let maxScroll = 0;
    const trackScrollDepth = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
        maxScroll = scrollPercent;
        if (window.analytics) {
          window.analytics.trackScrollDepth(scrollPercent);
        }
      }
    };

    // Track time on page
    const startTime = Date.now();
    const trackTimeOnPage = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      if (timeSpent > 30 && window.analytics) { // Track after 30 seconds
        window.analytics.trackCustomEvent('time_on_page', {
          event_category: 'Engagement',
          value: timeSpent
        });
      }
    };

    window.addEventListener('scroll', trackScrollDepth, { passive: true });
    window.addEventListener('beforeunload', trackTimeOnPage);

    return () => {
      window.removeEventListener('scroll', trackScrollDepth);
      window.removeEventListener('beforeunload', trackTimeOnPage);
    };
  }, []);

  return (
    <>
      {children}
      <GoogleAnalytics />
      {process.env.NODE_ENV === 'production' && (
        <>
          <VercelAnalytics />
          <SpeedInsights />
        </>
      )}
    </>
  );
}

// Legacy export for backward compatibility
export function Analytics() {
  return (
    <>
      <GoogleAnalytics />
      {process.env.NODE_ENV === 'production' && (
        <>
          <VercelAnalytics />
          <SpeedInsights />
        </>
      )}
    </>
  );
}

// Main exports
export { default as GoogleAnalytics, analytics } from './GoogleAnalytics';
export { AnalyticsProvider as default };