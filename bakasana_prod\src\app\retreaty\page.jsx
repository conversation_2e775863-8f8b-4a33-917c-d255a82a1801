import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getAllDestinations, destinations } from '@/data/programData';
import { SEOHead } from '@/components/SEO';
import { generateRetreatStructuredData } from '@/lib/yogaStructuredData';
import { metadata } from './metadata';

// Export metadata for Next.js
export { metadata };

// Advanced metadata for the main retreats page
const pageMetadata = {
  title: 'Retreaty Jogi 2025 - Bali & Sri Lanka | Transformacyjne Podróże BAKASANA',
  description: 'Odk<PERSON>j najlepsze retreaty jogi z certyfikowaną instruktorką Julią Jakubowicz. Bali (Ubud, Canggu, Gili Air) i Sri Lanka (Sigiriya, Południe). Małe grupy, luksusowe hotele, transformacyjne doświadczenia.',
  keywords: [
    'retreaty jogi 2025',
    'retreat jogi bali',
    'retreat jogi sri lanka',
    'julia j<PERSON><PERSON> joga',
    'transformacyjne podróże',
    'ubud yoga retreat',
    'canggu retreat',
    'gili air joga',
    'sigiriya yoga',
    'małe grupy retreat',
    'luksusowe retreaty',
    'duchowe wakacje azja'
  ],
  structuredData: {
    "@context": "https://schema.org",
    "@type": "TravelAgency",
    "name": "BAKASANA Retreaty Jogi",
    "description": "Retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz",
    "offers": {
      "@type": "Offer",
      "category": "Yoga Retreats",
      "priceRange": "2900-4200 PLN"
    }
  },
  canonicalUrl: 'https://bakasana-travel.blog/retreaty',
  imageUrl: 'https://bakasana-travel.blog/images/og/retreaty-overview-2025.jpg',
};

export default async function RetreatyPage() {
  const allDestinations = getAllDestinations();
  
  // Organize destinations by country
  const baliDestinations = allDestinations.filter(dest => dest.country === 'Bali');
  const sriLankaDestinations = allDestinations.filter(dest => dest.country === 'Sri Lanka');

  const coreValues = [
    {
      title: 'Autentyczność',
      description: 'Prawdziwe połączenie z tradycyjną jogą i lokalną kulturą'
    },
    {
      title: 'Ciepło',
      description: 'Starannie wybrane miejsca, gdzie czujesz się jak w domu'
    },
    {
      title: 'Transformacja',
      description: 'Holistyczne podejście do powrotu do siebie'
    },
    {
      title: 'Przyjaźń',
      description: 'Małe grupy, gdzie każda z was jest widziana i ważna'
    }
  ];

  const upcomingRetreats = [
    {
      destination: 'Bali - Ubud',
      dates: '15-25 marca 2025',
      price: '3200 PLN',
      spots: '4 wolne miejsca',
      highlight: true
    },
    {
      destination: 'Sri Lanka - Południe',
      dates: '10-20 maja 2025',
      price: '2900 PLN',
      spots: '6 wolnych miejsc',
      highlight: false
    },
    {
      destination: 'Bali - Gili Air',
      dates: '15-25 czerwca 2025',
      price: '3400 PLN',
      spots: 'Wkrótce otwarte',
      highlight: false
    }
  ];

  return (
    <main className="bg-sanctuary min-h-screen">
      {/* SEO handled by metadata export */}

      {/* HERO SECTION - Old Money Elegance */}
      <section className="py-24 md:py-32 px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="w-16 h-px bg-temple-gold/40 mx-auto mb-8 md:mb-12 animate-fade-in"></div>
          
          <h1 className="font-primary text-4xl md:text-6xl lg:text-7xl font-light text-charcoal mb-6 md:mb-8 leading-tight tracking-wide">
            Retreaty
          </h1>

          <p className="text-base md:text-lg text-charcoal/70 font-secondary mb-12 md:mb-16 max-w-2xl mx-auto leading-relaxed italic">
            Duchowe przygody na Bali i Sri Lanka - jak podróż z najlepszą przyjaciółką
          </p>

          <div className="text-xs text-charcoal/50 font-secondary tracking-widest uppercase">
            Transformacyjne doświadczenia w małych grupach pełnych ciepła
          </div>
          
          <div className="w-16 h-px bg-temple-gold/40 mx-auto mt-8 md:mt-12 animate-fade-in"></div>
        </div>
      </section>

      {/* CORE VALUES - Minimalist Elegance */}
      <section className="py-24 px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-16 lg:gap-20">
            {coreValues.map((value, index) => (
              <div key={index} className="text-center group opacity-0 animate-fade-in" style={{animationDelay: `${index * 0.2}s`}}>
                <div className="mb-8">
                  <div className="w-16 h-16 mx-auto border border-temple-gold/20 flex items-center justify-center group-hover:border-temple-gold/40 transition-all duration-500">
                    <div className="w-3 h-3 bg-temple-gold/60 rounded-full group-hover:bg-temple-gold/80 group-hover:scale-125 transition-all duration-500"></div>
                  </div>
                </div>
                <h3 className="font-primary text-xl font-light text-charcoal mb-6 tracking-wide">
                  {value.title}
                </h3>
                <p className="text-sm text-charcoal/60 font-secondary leading-relaxed max-w-48 mx-auto">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* DESTINATIONS - Sophisticated Grid */}
      <section className="py-20 px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="font-primary text-4xl md:text-5xl font-light text-charcoal mb-8">
              Nasze Destinacje
            </h2>
            <div className="w-16 h-px bg-temple-gold/40 mx-auto mb-8"></div>
            <p className="text-lg text-charcoal/70 font-secondary max-w-2xl mx-auto leading-relaxed">
              Starannie wybrane lokalizacje oferujące autentyczne połączenie praktyki jogi
              z odkrywaniem najpiękniejszych zakątków duchowej Azji
            </p>
          </div>

          {/* Bali Section */}
          <div className="mb-20">
            <div className="flex items-center justify-center mb-12">
              <div className="flex-1 h-px bg-temple-gold/20"></div>
              <h3 className="font-primary text-2xl font-light text-charcoal px-8">Bali</h3>
              <div className="flex-1 h-px bg-temple-gold/20"></div>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12">
              {baliDestinations.map((destination, index) => (
                <Link
                  key={destination.id}
                  href={`/program?destination=${destination.id}`}
                  className="group block opacity-0 animate-fade-in"
                  style={{animationDelay: `${index * 0.15}s`}}
                >
                  <div className="relative h-96 overflow-hidden mb-8">
                    <Image
                      src={destination.image}
                      alt={`Retreat jogi ${destination.name}`}
                      fill
                      className="object-cover object-center transition-transform duration-700 group-hover:scale-105 grayscale-[0.1] group-hover:grayscale-0"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-charcoal/20 via-transparent to-transparent group-hover:from-charcoal/40 transition-all duration-500" />
                  </div>
                  
                  <div className="text-center">
                    <h4 className="font-primary text-xl font-light text-charcoal mb-3 group-hover:text-temple-gold transition-colors duration-300">
                      {destination.name}
                    </h4>
                    <p className="text-sm text-charcoal/60 font-secondary mb-4 tracking-wide">
                      {destination.duration}
                    </p>
                    <div className="text-temple-gold font-primary font-light text-lg tracking-wide">
                      {destination.price || 'Od 3200 PLN'}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Sri Lanka Section */}
          <div>
            <div className="flex items-center justify-center mb-12">
              <div className="flex-1 h-px bg-temple-gold/20"></div>
              <h3 className="font-primary text-2xl font-light text-charcoal px-8">Sri Lanka</h3>
              <div className="flex-1 h-px bg-temple-gold/20"></div>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12">
              {sriLankaDestinations.map((destination, index) => (
                <Link
                  key={destination.id}
                  href={`/program?destination=${destination.id}`}
                  className="group block opacity-0 animate-fade-in"
                  style={{animationDelay: `${(index + baliDestinations.length) * 0.15}s`}}
                >
                  <div className="relative h-96 overflow-hidden mb-8">
                    <Image
                      src={destination.image}
                      alt={`Retreat jogi ${destination.name}`}
                      fill
                      className="object-cover object-center transition-transform duration-700 group-hover:scale-105 grayscale-[0.1] group-hover:grayscale-0"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-charcoal/20 via-transparent to-transparent group-hover:from-charcoal/40 transition-all duration-500" />
                  </div>
                  
                  <div className="text-center">
                    <h4 className="font-primary text-xl font-light text-charcoal mb-3 group-hover:text-temple-gold transition-colors duration-300">
                      {destination.name}
                    </h4>
                    <p className="text-sm text-charcoal/60 font-secondary mb-4 tracking-wide">
                      {destination.duration}
                    </p>
                    <div className="text-temple-gold font-primary font-light text-lg tracking-wide">
                      {destination.price || 'Od 2900 PLN'}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* UPCOMING RETREATS - Elegant Showcase */}
      <section className="py-20 px-6 lg:px-8 bg-rice/30">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="font-primary text-4xl md:text-5xl font-light text-charcoal mb-6">
              Nadchodzące Retreaty
            </h2>
            <div className="w-16 h-px bg-temple-gold/40 mx-auto mb-8"></div>
            <p className="text-lg text-charcoal/70 font-secondary max-w-2xl mx-auto leading-relaxed">
              Zarezerwuj swoje miejsce w jednym z najbliższych wyjazdów
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {upcomingRetreats.map((retreat, index) => (
              <div
                key={index}
                className={`p-8 text-center transition-all duration-500 hover:transform hover:-translate-y-2 ${
                  retreat.highlight ? 'bg-sanctuary border-l-2 border-temple-gold' : 'bg-sanctuary/50'
                }`}
              >
                <div className="space-y-4">
                  <h3 className="font-primary text-xl font-light text-charcoal">
                    {retreat.destination}
                  </h3>
                  
                  <div className="space-y-2 text-sm text-charcoal/60 font-secondary">
                    <div>{retreat.dates}</div>
                    <div className="text-temple-gold font-primary">Od {retreat.price}</div>
                    <div className={retreat.spots.includes('Wkrótce') ? 'text-charcoal/40' : 'text-sage-green'}>
                      {retreat.spots}
                    </div>
                  </div>
                  
                  <div className="pt-4">
                    <Link
                      href="/rezerwacja"
                      className="inline-block border border-temple-gold/40 text-temple-gold px-8 py-3 text-sm font-secondary tracking-wide transition-all duration-300 hover:bg-temple-gold hover:text-sanctuary"
                    >
                      {retreat.spots.includes('Wkrótce') ? 'Lista Oczekujących' : 'Rezerwuj Miejsce'}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* TESTIMONIALS - Refined Elegance */}
      <section className="py-32 px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="w-24 h-px bg-temple-gold/40 mx-auto mb-16"></div>
          
          <blockquote className="font-primary text-2xl md:text-3xl lg:text-4xl font-light text-charcoal leading-relaxed mb-12 italic">
            "Każda podróż zaczyna się od jednego kroku, każda transformacja od jednej decyzji..."
          </blockquote>
          
          <div className="w-24 h-px bg-temple-gold/40 mx-auto"></div>
        </div>
      </section>

      {/* CALL TO ACTION - Sophisticated Elegance */}
      <section className="py-32 px-6 lg:px-8 bg-charcoal text-sanctuary">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="font-primary text-4xl md:text-5xl font-light mb-12">
            Gotowa na
            <span className="text-temple-gold block mt-2">Transformację?</span>
          </h2>
          
          <p className="text-lg text-sanctuary/80 font-secondary mb-16 leading-relaxed max-w-2xl mx-auto">
            Rozpocznij swoją podróż ku głębszemu zrozumieniu siebie i odnajdź wewnętrzny spokój
            w przepięknych zakątkach duchowej Azji
          </p>
          
          <div className="flex flex-col sm:flex-row gap-8 justify-center items-center">
            <Link
              href="/rezerwacja"
              className="border border-temple-gold text-temple-gold px-16 py-4 text-sm font-secondary tracking-widest transition-all duration-500 hover:bg-temple-gold hover:text-charcoal hover:transform hover:-translate-y-1"
            >
              ZAREZERWUJ KONSULTACJĘ
            </Link>
            <Link
              href="/kontakt"
              className="border border-sanctuary/30 text-sanctuary/80 px-16 py-4 text-sm font-secondary tracking-widest transition-all duration-500 hover:bg-sanctuary/10 hover:text-sanctuary hover:transform hover:-translate-y-1"
            >
              POROZMAWIAJMY
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}