/**
 * LAZY BOOKING FLOW
 * Dynamic import wrapper for EnhancedBookingFlow
 * Reduces initial bundle size by loading booking component on demand
 */

import { lazy, Suspense } from 'react';
import { LoadingStates } from '../Performance/LoadingStates';

// Dynamic import with error handling
const EnhancedBookingFlow = lazy(() => 
  import('./EnhancedBookingFlow').catch(error => {
    console.error('Failed to load Booking Flow:', error);
    // Return a fallback component
    return {
      default: () => (
        <div className="min-h-screen flex items-center justify-center bg-sanctuary">
          <div className="text-center p-8 max-w-md">
            <div className="w-20 h-20 mx-auto mb-6 bg-temple-gold/20 rectangular flex items-center justify-center">
              <span className="text-temple-gold text-3xl">📅</span>
            </div>
            <h2 className="text-2xl font-playfair text-charcoal mb-4">
              Booking System Unavailable
            </h2>
            <p className="text-ash text-sm mb-6 leading-relaxed">
              The booking system could not be loaded at this time. 
              Please try refreshing the page or contact us directly.
            </p>
            <div className="space-y-3">
              <button 
                onClick={() => window.location.reload()}
                className="w-full px-6 py-3 bg-temple-gold text-sanctuary rectangular hover:bg-enterprise-brown transition-colors font-medium"
              >
                Refresh Page
              </button>
              <a 
                href="mailto:<EMAIL>"
                className="block w-full px-6 py-3 border border-temple-gold text-temple-gold rectangular hover:bg-temple-gold hover:text-sanctuary transition-colors font-medium"
              >
                Contact Us Directly
              </a>
            </div>
          </div>
        </div>
      )
    };
  })
);

// Booking flow loading skeleton
const BookingFlowLoader = () => (
  <div className="min-h-screen bg-sanctuary">
    {/* Header */}
    <div className="bg-white border-b border-stone-light/30">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center space-y-4">
          <div className="h-10 w-80 mx-auto bg-stone-light/50 rectangular animate-pulse"></div>
          <div className="h-4 w-96 mx-auto bg-stone-light/30 rectangular animate-pulse"></div>
        </div>
      </div>
    </div>

    {/* Progress indicator skeleton */}
    <div className="bg-white border-b border-stone-light/30">
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="flex items-center justify-center space-x-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-temple-gold/30 rectangular animate-pulse"></div>
              <div className="h-4 w-20 bg-stone-light/30 rectangular animate-pulse"></div>
              {i < 3 && <div className="w-8 h-0.5 bg-stone-light/30 animate-pulse"></div>}
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Main content */}
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left column - Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Form sections */}
          {[...Array(3)].map((_, sectionIndex) => (
            <div key={sectionIndex} className="bg-white p-6 rectangular border border-stone-light/30">
              <div className="h-6 w-48 bg-stone-light/50 rectangular animate-pulse mb-6"></div>
              
              {/* Form fields */}
              <div className="space-y-4">
                {[...Array(3)].map((_, fieldIndex) => (
                  <div key={fieldIndex}>
                    <div className="h-4 w-24 bg-stone-light/30 rectangular animate-pulse mb-2"></div>
                    <div className="h-12 w-full bg-stone-light/20 rectangular animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {/* Action buttons */}
          <div className="flex justify-between">
            <div className="h-12 w-24 bg-stone-light/30 rectangular animate-pulse"></div>
            <div className="h-12 w-32 bg-temple-gold/30 rectangular animate-pulse"></div>
          </div>
        </div>

        {/* Right column - Summary */}
        <div className="space-y-6">
          {/* Booking summary */}
          <div className="bg-white p-6 rectangular border border-stone-light/30 sticky top-8">
            <div className="h-6 w-32 bg-stone-light/50 rectangular animate-pulse mb-6"></div>
            
            {/* Summary items */}
            <div className="space-y-4 mb-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="flex justify-between items-center">
                  <div className="h-4 w-24 bg-stone-light/30 rectangular animate-pulse"></div>
                  <div className="h-4 w-16 bg-stone-light/50 rectangular animate-pulse"></div>
                </div>
              ))}
            </div>

            {/* Total */}
            <div className="border-t border-stone-light/30 pt-4">
              <div className="flex justify-between items-center">
                <div className="h-5 w-20 bg-stone-light/50 rectangular animate-pulse"></div>
                <div className="h-6 w-24 bg-temple-gold/30 rectangular animate-pulse"></div>
              </div>
            </div>

            {/* CTA button */}
            <div className="mt-6">
              <div className="h-12 w-full bg-temple-gold/30 rectangular animate-pulse"></div>
            </div>
          </div>

          {/* Additional info */}
          <div className="bg-white p-6 rectangular border border-stone-light/30">
            <div className="h-5 w-28 bg-stone-light/50 rectangular animate-pulse mb-4"></div>
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-3 w-full bg-stone-light/20 rectangular animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Loading indicator */}
    <div className="fixed bottom-8 right-8">
      <div className="bg-white p-4 rectangular border border-stone-light/30 shadow-elegant">
        <div className="flex items-center space-x-3">
          <LoadingStates.Breathing />
          <span className="text-sm text-ash">Loading booking system...</span>
        </div>
      </div>
    </div>
  </div>
);

// Main component with error boundary
export default function LazyBookingFlow(props) {
  return (
    <Suspense fallback={<BookingFlowLoader />}>
      <EnhancedBookingFlow {...props} />
    </Suspense>
  );
}

// Export loading component for reuse
export { BookingFlowLoader };
