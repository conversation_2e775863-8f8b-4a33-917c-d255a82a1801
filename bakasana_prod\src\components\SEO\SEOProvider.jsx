/**
 * 🌐 BAKASANA - UNIFIED SEO PROVIDER
 * 
 * Kontekst SEO dla całej aplikacji:
 * - Globalne ustawienia SEO
 * - Automatyczne structured data
 * - Breadcrumbs tracking
 * - Page tracking
 * - Meta tags management
 * 
 * Usage:
 * <SEOProvider>
 *   <App />
 * </SEOProvider>
 */

'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import StructuredData, { OrganizationSchema, PersonSchema, BreadcrumbSchema } from './StructuredData';

const SEOContext = createContext({});

export const useSEO = () => {
  const context = useContext(SEOContext);
  if (!context) {
    throw new Error('useSEO must be used within SEOProvider');
  }
  return context;
};

const SEOProvider = ({ children }) => {
  const pathname = usePathname();
  const [breadcrumbs, setBreadcrumbs] = useState([]);
  const [currentPage, setCurrentPage] = useState({});

  // Generate breadcrumbs from pathname
  useEffect(() => {
    const generateBreadcrumbs = (path) => {
      const segments = path.split('/').filter(Boolean);
      const crumbs = [{ name: 'Strona główna', url: '/' }];
      
      let currentPath = '';
      segments.forEach((segment, index) => {
        currentPath += `/${segment}`;
        
        // Map segments to readable names
        const segmentNames = {
          'retreaty': 'Retreaty',
          'blog': 'Blog',
          'galeria': 'Galeria',
          'kontakt': 'Kontakt',
          'o-mnie': 'O mnie',
          'program': 'Program',
          'rezerwacja': 'Rezerwacja',
          'zajecia-online': 'Zajęcia Online',
          'bali': 'Bali',
          'srilanka': 'Sri Lanka',
          'julia-jakubowicz-instruktor': 'Julia Jakubowicz',
          'retreaty-jogi-bali-2025': 'Retreaty Jogi Bali 2025',
          'yoga-retreat-z-polski': 'Yoga Retreat z Polski'
        };
        
        const name = segmentNames[segment] || segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        crumbs.push({ name, url: currentPath });
      });
      
      return crumbs;
    };

    setBreadcrumbs(generateBreadcrumbs(pathname));
  }, [pathname]);

  // Update current page info
  useEffect(() => {
    const pageInfo = {
      path: pathname,
      timestamp: new Date().toISOString(),
      breadcrumbs: breadcrumbs
    };
    setCurrentPage(pageInfo);
  }, [pathname, breadcrumbs]);

  // SEO utilities
  const updatePageSEO = (seoData) => {
    setCurrentPage(prev => ({ ...prev, ...seoData }));
  };

  const addBreadcrumb = (name, url) => {
    setBreadcrumbs(prev => [...prev, { name, url }]);
  };

  const resetBreadcrumbs = () => {
    setBreadcrumbs([{ name: 'Strona główna', url: '/' }]);
  };

  const contextValue = {
    breadcrumbs,
    currentPage,
    updatePageSEO,
    addBreadcrumb,
    resetBreadcrumbs,
    pathname
  };

  return (
    <SEOContext.Provider value={contextValue}>
      {/* Global structured data */}
      <OrganizationSchema />
      <PersonSchema />
      
      {/* Dynamic breadcrumbs */}
      {breadcrumbs.length > 1 && (
        <BreadcrumbSchema breadcrumbs={breadcrumbs} />
      )}
      
      {children}
    </SEOContext.Provider>
  );
};

export default SEOProvider;