/**
 * 📋 BAKASANA - UNIFIED STRUCTURED DATA COMPONENT
 * 
 * Skonsolidowany komponent Schema.org łączący wszystkie structured data:
 * - Organization schema
 * - Person schema (<PERSON>)
 * - Service schema (Yoga retreats)
 * - Event schema (Retreats)
 * - Article schema (Blog posts)
 * - Breadcrumb schema
 * - Contact Point schema
 * - Local Business schema
 * 
 * Usage:
 * <StructuredData
 *   type="organization"
 *   data={{
 *     name: "Bakasana Studio",
 *     description: "Retreaty jogi na Bali"
 *   }}
 * />
 */

'use client';

import { useEffect } from 'react';

// Base schemas
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://bakasana-travel.blog';

const ORGANIZATION_SCHEMA = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Bakasana Studio",
  "alternateName": "Bakasana Travel Blog",
  "description": "Transformacyjne retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz",
  "url": BASE_URL,
  "logo": `${BASE_URL}/images/logo/bakasana-logo.png`,
  "image": `${BASE_URL}/images/og/organization.jpg`,
  "foundingDate": "2020",
  "founder": {
    "@type": "Person",
    "name": "Julia Jakubowicz",
    "jobTitle": "Certyfikowana Instruktorka Jogi",
    "url": `${BASE_URL}/julia-jakubowicz-instruktor`
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+48-666-777-888",
    "email": "<EMAIL>",
    "contactType": "customer service",
    "availableLanguage": ["Polish", "English"],
    "areaServed": ["PL", "EU", "US", "CA", "AU"],
    "hoursAvailable": {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
      "opens": "09:00",
      "closes": "21:00"
    }
  },
  "sameAs": [
    "https://www.instagram.com/bakasana.studio",
    "https://www.facebook.com/bakasana.studio"
  ],
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "PL",
    "addressLocality": "Rzeszów",
    "addressRegion": "Podkarpackie"
  }
};

const PERSON_SCHEMA = {
  "@context": "https://schema.org",
  "@type": "Person",
  "name": "Julia Jakubowicz",
  "jobTitle": "Certyfikowana Instruktorka Jogi",
  "description": "Certyfikowana instruktorka jogi z wieloletnim doświadczeniem, specjalizująca się w retreatach transformacyjnych na Bali i Sri Lanka",
  "url": `${BASE_URL}/julia-jakubowicz-instruktor`,
  "image": `${BASE_URL}/images/profile/julia-jakubowicz.jpg`,
  "sameAs": [
    "https://www.instagram.com/bakasana.studio",
    "https://www.facebook.com/bakasana.studio"
  ],
  "worksFor": {
    "@type": "Organization",
    "name": "Bakasana Studio"
  },
  "knowsAbout": [
    "Hatha Yoga",
    "Vinyasa Yoga",
    "Ashtanga Yoga",
    "Meditation",
    "Wellness Retreats",
    "Transformational Travel"
  ],
  "nationality": "Polish",
  "birthPlace": "Poland"
};

const generateServiceSchema = (service) => ({
  "@context": "https://schema.org",
  "@type": "Service",
  "name": service.name,
  "description": service.description,
  "provider": {
    "@type": "Organization",
    "name": "Bakasana Studio"
  },
  "areaServed": ["PL", "EU", "US", "CA", "AU"],
  "serviceType": "Yoga Retreat",
  "category": "Wellness & Fitness",
  "offers": {
    "@type": "Offer",
    "price": service.price,
    "priceCurrency": "PLN",
    "availability": "https://schema.org/InStock",
    "validFrom": service.validFrom,
    "validThrough": service.validThrough
  }
});

const generateEventSchema = (event) => ({
  "@context": "https://schema.org",
  "@type": "Event",
  "name": event.name,
  "description": event.description,
  "startDate": event.startDate,
  "endDate": event.endDate,
  "location": {
    "@type": "Place",
    "name": event.location.name,
    "address": {
      "@type": "PostalAddress",
      "addressCountry": event.location.country,
      "addressLocality": event.location.city
    }
  },
  "organizer": {
    "@type": "Organization",
    "name": "Bakasana Studio",
    "url": BASE_URL
  },
  "offers": {
    "@type": "Offer",
    "price": event.price,
    "priceCurrency": "PLN",
    "availability": "https://schema.org/InStock",
    "url": `${BASE_URL}/retreaty/${event.slug}`
  },
  "image": event.image,
  "eventStatus": "https://schema.org/EventScheduled",
  "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode"
});

const generateArticleSchema = (article) => ({
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": article.title,
  "description": article.description,
  "image": article.image,
  "author": {
    "@type": "Person",
    "name": "Julia Jakubowicz",
    "url": `${BASE_URL}/julia-jakubowicz-instruktor`
  },
  "publisher": {
    "@type": "Organization",
    "name": "Bakasana Studio",
    "logo": {
      "@type": "ImageObject",
      "url": `${BASE_URL}/images/logo/bakasana-logo.png`
    }
  },
  "datePublished": article.publishedTime,
  "dateModified": article.modifiedTime || article.publishedTime,
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": `${BASE_URL}/blog/${article.slug}`
  },
  "articleSection": article.category,
  "keywords": article.tags?.join(', '),
  "wordCount": article.wordCount,
  "inLanguage": "pl-PL"
});

const generateBreadcrumbSchema = (breadcrumbs) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": breadcrumbs.map((crumb, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": crumb.name,
    "item": `${BASE_URL}${crumb.url}`
  }))
});

const StructuredData = ({ 
  type = 'organization', 
  data = {}, 
  breadcrumbs = [],
  ...props 
}) => {
  useEffect(() => {
    let schema = {};

    switch (type) {
      case 'organization':
        schema = { ...ORGANIZATION_SCHEMA, ...data };
        break;
      case 'person':
        schema = { ...PERSON_SCHEMA, ...data };
        break;
      case 'service':
        schema = generateServiceSchema(data);
        break;
      case 'event':
        schema = generateEventSchema(data);
        break;
      case 'article':
        schema = generateArticleSchema(data);
        break;
      case 'breadcrumb':
        schema = generateBreadcrumbSchema(breadcrumbs);
        break;
      default:
        schema = data;
    }

    // Create or update script tag
    const scriptId = `structured-data-${type}`;
    let scriptElement = document.getElementById(scriptId);
    
    if (!scriptElement) {
      scriptElement = document.createElement('script');
      scriptElement.id = scriptId;
      scriptElement.type = 'application/ld+json';
      document.head.appendChild(scriptElement);
    }
    
    scriptElement.textContent = JSON.stringify(schema);

    // Cleanup function
    return () => {
      const element = document.getElementById(scriptId);
      if (element) {
        element.remove();
      }
    };
  }, [type, data, breadcrumbs]);

  return null; // This component doesn't render anything visible
};

// Specialized components for common use cases
export const OrganizationSchema = (props) => (
  <StructuredData type="organization" {...props} />
);

export const PersonSchema = (props) => (
  <StructuredData type="person" {...props} />
);

export const ServiceSchema = (props) => (
  <StructuredData type="service" {...props} />
);

export const EventSchema = (props) => (
  <StructuredData type="event" {...props} />
);

export const ArticleSchema = (props) => (
  <StructuredData type="article" {...props} />
);

export const BreadcrumbSchema = ({ breadcrumbs, ...props }) => (
  <StructuredData type="breadcrumb" breadcrumbs={breadcrumbs} {...props} />
);

export default StructuredData;