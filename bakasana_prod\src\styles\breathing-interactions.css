/* =============================================
   🌸 BAKASANA - BREATHING MICRO-INTERACTIONS
   Subtle animations that feel like breathing
   ============================================= */

/* ===== BREATHING KEYFRAMES ===== */
@keyframes breathe {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.02);
    opacity: 0.95;
  }
}

@keyframes breathe-gentle {
  0%, 100% { 
    transform: translateY(0px);
    opacity: 1;
  }
  50% { 
    transform: translateY(-2px);
    opacity: 0.98;
  }
}

@keyframes breathe-text {
  0%, 100% { 
    letter-spacing: 0.05em;
    opacity: 1;
  }
  50% { 
    letter-spacing: 0.06em;
    opacity: 0.95;
  }
}

@keyframes breathe-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(212, 165, 116, 0.1);
  }
  50% { 
    box-shadow: 0 0 30px rgba(212, 165, 116, 0.2);
  }
}

@keyframes breathe-underline {
  0%, 100% { 
    width: 0%;
    opacity: 0.7;
  }
  50% { 
    width: 100%;
    opacity: 1;
  }
}

/* ===== BREATHING UTILITY CLASSES ===== */

/* Main breathing animation for cards and sections */
.breathe {
  animation: breathe 4s ease-in-out infinite;
}

/* Gentle breathing for text elements */
.breathe-gentle {
  animation: breathe-gentle 5s ease-in-out infinite;
}

/* Text breathing for headings */
.breathe-text {
  animation: breathe-text 6s ease-in-out infinite;
}

/* Glow breathing for special elements */
.breathe-glow {
  animation: breathe-glow 3s ease-in-out infinite;
}

/* ===== WARM UNDERLINE SYSTEM ===== */

/* Rose gold underline for key words */
.warm-underline {
  position: relative;
  display: inline-block;
  color: var(--charcoal);
  transition: color 0.3s ease;
}

.warm-underline::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--rose-gold), var(--rose-gold-warm));
  transition: width 0.4s ease;
}

.warm-underline:hover::after,
.warm-underline.active::after {
  width: 100%;
}

.warm-underline:hover {
  color: var(--rose-gold-warm);
}

/* Breathing underline animation */
.warm-underline.breathe-underline::after {
  animation: breathe-underline 3s ease-in-out infinite;
}

/* ===== ENHANCED TYPOGRAPHY WITH BREATHING ===== */

/* Playfair Display headings with breathing */
.heading-playfair {
  font-family: var(--font-primary);
  font-weight: 400;
  line-height: var(--leading-breathing);
  letter-spacing: 0.02em;
  color: var(--charcoal);
}

.heading-playfair.breathe-text {
  animation: breathe-text 6s ease-in-out infinite;
}

/* Body text with enhanced breathing room */
.body-breathing {
  font-family: var(--font-secondary);
  line-height: var(--leading-breathing);
  color: var(--charcoal);
  letter-spacing: 0.01em;
}

/* Handwriting font for personal touches */
.text-handwriting {
  font-family: var(--font-accent);
  color: var(--rose-gold-warm);
  font-size: 1.1em;
  line-height: var(--leading-relaxed);
}

/* ===== HOVER INTERACTIONS WITH BREATHING ===== */

/* Breathing hover for buttons */
.btn-breathing {
  transition: all 0.3s ease;
  border: 1px solid var(--rose-gold);
  color: var(--charcoal);
  background: transparent;
  padding: 0.75rem 2rem;
  font-family: var(--font-secondary);
  font-size: 0.875rem;
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.btn-breathing:hover {
  background: var(--rose-gold-light);
  color: var(--charcoal);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.2);
  animation: breathe-gentle 2s ease-in-out infinite;
}

/* Breathing hover for cards */
.card-breathing {
  transition: all 0.3s ease;
  background: var(--sanctuary);
  border: 1px solid var(--stone-light);
  padding: 2rem;
}

.card-breathing:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  animation: breathe 3s ease-in-out infinite;
}

/* ===== SECTION BREATHING EFFECTS ===== */

/* Hero section breathing */
.hero-breathing {
  background: linear-gradient(180deg, 
    var(--dawn-cream) 0%, 
    var(--dawn-peach) 40%, 
    var(--warm-sand) 100%
  );
  animation: breathe-gentle 8s ease-in-out infinite;
}

/* Section dividers with breathing */
.section-divider {
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--rose-gold-light) 50%, 
    transparent 100%
  );
  margin: 4rem 0;
  animation: breathe-glow 4s ease-in-out infinite;
}

/* ===== ACCESSIBILITY CONSIDERATIONS ===== */

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .breathe,
  .breathe-gentle,
  .breathe-text,
  .breathe-glow,
  .breathe-underline,
  .hero-breathing,
  .section-divider,
  .btn-breathing:hover,
  .card-breathing:hover {
    animation: none !important;
  }
  
  /* Keep hover effects but remove animations */
  .btn-breathing:hover {
    background: var(--rose-gold-light);
    color: var(--charcoal);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(212, 165, 116, 0.2);
  }
  
  .card-breathing:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-elevated);
  }
}

/* ===== FOCUS STATES WITH BREATHING ===== */

/* Enhanced focus states */
.breathe:focus,
.breathe-gentle:focus,
.btn-breathing:focus,
.card-breathing:focus {
  outline: 2px solid var(--rose-gold);
  outline-offset: 2px;
  animation: breathe-glow 2s ease-in-out infinite;
}

@media (prefers-reduced-motion: reduce) {
  .breathe:focus,
  .breathe-gentle:focus,
  .btn-breathing:focus,
  .card-breathing:focus {
    animation: none !important;
    box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.3);
  }
}

/* ===== PHOTO STYLE GUIDE EFFECTS ===== */

/* Photo filters for warm elegance transformation */
.photo-golden-hour {
  filter: brightness(1.1) contrast(0.95) saturate(1.2) sepia(0.1) hue-rotate(10deg);
  transition: filter 0.3s ease;
}

.photo-authentic {
  filter: brightness(1.05) contrast(0.9) saturate(1.1) blur(0.3px);
  transition: filter 0.3s ease;
}

.photo-memory {
  filter: brightness(1.08) contrast(0.92) saturate(1.15) blur(0.5px) sepia(0.05);
  transition: filter 0.3s ease;
}

.photo-warm-detail {
  filter: brightness(1.12) contrast(0.88) saturate(1.25) sepia(0.08);
  transition: filter 0.3s ease;
}

/* Photo container effects */
.photo-container {
  position: relative;
  overflow: hidden;
  transition: transform 0.5s ease;
}

.photo-container:hover {
  transform: scale(1.02);
}

.photo-container:hover .photo-image {
  transform: scale(1.05);
}

.photo-image {
  transition: transform 0.7s ease;
}

/* Photo overlays for warm effect */
.photo-overlay-golden {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(255, 223, 186, 0.1) 0%, rgba(255, 239, 213, 0.05) 100%);
  mix-blend-mode: soft-light;
  pointer-events: none;
}

.photo-overlay-warm {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(255, 245, 235, 0.1) 0%, transparent 70%);
  mix-blend-mode: soft-light;
  pointer-events: none;
}

.photo-overlay-authentic {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(232, 180, 184, 0.08) 0%, rgba(212, 165, 116, 0.06) 100%);
  mix-blend-mode: soft-light;
  pointer-events: none;
}

/* Photo breathing effects */
.photo-breathe {
  animation: breathe-gentle 6s ease-in-out infinite;
}

.photo-breathe:hover {
  animation: breathe 3s ease-in-out infinite;
}

/* Gallery grid with breathing */
.photo-gallery {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.photo-gallery .photo-container {
  aspect-ratio: 4/5;
  border-radius: 0; /* Keep sharp edges for elegance */
}

/* Caption styling */
.photo-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
  color: white;
  font-family: var(--font-accent);
  font-style: italic;
  opacity: 0.9;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.photo-container:hover .photo-caption {
  transform: translateY(0);
}