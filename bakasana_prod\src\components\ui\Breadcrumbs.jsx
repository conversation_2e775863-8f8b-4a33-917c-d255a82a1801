/**
 * 🧭 BAKASANA - UNIFIED BREADCRUMBS COMPONENT
 * 
 * Skonsolidowany komponent breadcrumbs łączący funkcje z 4 poprzednich implementacji:
 * - Automatyczne generowanie z pathname
 * - Custom breadcrumbs
 * - SEO structured data
 * - Accessibility features
 * - Różne warianty stylizacji
 * 
 * Usage:
 * <Breadcrumbs />
 * <Breadcrumbs variant="minimal" />
 * <Breadcrumbs customItems={[{label: "Home", href: "/"}, {label: "About"}]} />
 */

'use client';

import { useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

// Breadcrumb labels mapping
const BREADCRUMB_LABELS = {
  '': 'Strona główna',
  'retreaty': 'Retreaty',
  'program': 'Program',
  'zajecia-online': 'Zajęcia Online',
  'blog': 'Blog',
  'o-mnie': 'O mnie',
  'kontakt': 'Kontakt',
  'galeria': 'Galeria',
  'rezerwacja': 'Rezerwacja',
  'polityka-prywatnosci': 'Polityka Prywatności',
  'bali': 'Bali',
  'srilanka': 'Sri Lanka',
  'ubud': 'Ubud',
  'canggu': 'Canggu',
  'galle': 'Galle',
  'sigiriya': 'Sigiriya',
  'wellness': 'Wellness',
  'transformacyjne-podroze-azja': 'Transformacyjne Podróże',
  'yoga-retreat-z-polski': 'Yoga Retreat z Polski',
  'retreaty-jogi-bali-2025': 'Retreaty Jogi Bali 2025',
  'joga-sri-lanka-retreat': 'Joga Sri Lanka Retreat',
  'julia-jakubowicz-instruktor': 'Julia Jakubowicz - Instruktor',
  'mapa': 'Mapa',
  'admin': 'Panel Administracyjny',
  'bookings': 'Rezerwacje',
  'test-galeria': 'Test Galeria',
  'test-hero': 'Test Hero',
  'hero-demo': 'Hero Demo',
  'warm-elegance-demo': 'Warm Elegance Demo',
  'old-money': 'Old Money'
};

// Variant configurations
const VARIANTS = {
  default: {
    containerClass: 'mb-6',
    navClass: '',
    listClass: 'flex items-center space-x-2 text-sm',
    itemClass: 'flex items-center',
    linkClass: 'text-stone hover:text-charcoal transition-colors duration-300',
    currentClass: 'text-charcoal font-medium',
    separatorClass: 'w-4 h-4 text-stone/40 mx-2'
  },
  minimal: {
    containerClass: 'mb-4',
    navClass: '',
    listClass: 'flex items-center space-x-1 text-xs',
    itemClass: 'flex items-center',
    linkClass: 'text-stone/70 hover:text-stone transition-colors',
    currentClass: 'text-stone',
    separatorClass: 'w-3 h-3 text-stone/30 mx-1'
  },
  elegant: {
    containerClass: 'mb-8',
    navClass: 'border-b border-stone/10 pb-4',
    listClass: 'flex items-center space-x-3 text-sm',
    itemClass: 'flex items-center',
    linkClass: 'text-temple-gold/80 hover:text-temple-gold transition-colors duration-300 font-medium',
    currentClass: 'text-charcoal font-semibold',
    separatorClass: 'w-4 h-4 text-temple-gold/40 mx-3'
  },
  professional: {
    containerClass: 'mb-6 bg-whisper/50 px-6 py-3 rectangular',
    navClass: '',
    listClass: 'flex items-center space-x-2 text-sm',
    itemClass: 'flex items-center',
    linkClass: 'text-charcoal/70 hover:text-charcoal transition-colors duration-200',
    currentClass: 'text-charcoal font-medium',
    separatorClass: 'w-4 h-4 text-charcoal/30 mx-2'
  }
};

const ChevronRightIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
  </svg>
);

const HomeIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const Breadcrumbs = ({
  customItems = null,
  variant = 'default',
  showHome = true,
  showStructuredData = true,
  className = '',
  maxItems = 5,
  homeIcon = false,
  separator = 'chevron',
  ...props
}) => {
  const pathname = usePathname();
  const config = VARIANTS[variant] || VARIANTS.default;

  const breadcrumbs = useMemo(() => {
    // Use custom items if provided
    if (customItems) {
      return customItems;
    }

    // Don't show breadcrumbs on homepage
    if (pathname === '/') return [];

    const pathSegments = pathname.split('/').filter(Boolean);
    const crumbs = [];

    // Add home if showHome is true
    if (showHome) {
      crumbs.push({
        label: BREADCRUMB_LABELS[''],
        href: '/',
        isHome: true
      });
    }

    // Build breadcrumbs from path segments
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      const label = BREADCRUMB_LABELS[segment] || 
                   segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      
      crumbs.push({
        label,
        href: index === pathSegments.length - 1 ? null : currentPath, // Last item has no href
        isLast: index === pathSegments.length - 1
      });
    });

    // Limit items if maxItems is set
    if (maxItems && crumbs.length > maxItems) {
      const start = crumbs.slice(0, 1); // Keep home
      const end = crumbs.slice(-2); // Keep last 2 items
      const middle = [{ label: '...', href: null, isEllipsis: true }];
      return [...start, ...middle, ...end];
    }

    return crumbs;
  }, [customItems, pathname, showHome, maxItems]);

  // Don't render if no breadcrumbs
  if (breadcrumbs.length === 0) return null;

  const SeparatorIcon = separator === 'chevron' ? ChevronRightIcon : 
                       separator === 'slash' ? () => <span className={config.separatorClass}>/</span> :
                       ChevronRightIcon;

  return (
    <>
      <nav 
        aria-label="Breadcrumb" 
        className={cn(config.containerClass, className)}
        {...props}
      >
        <div className={config.navClass}>
          <ol className={config.listClass}>
            {breadcrumbs.map((item, index) => (
              <li key={index} className={config.itemClass}>
                {/* Separator */}
                {index > 0 && !item.isEllipsis && (
                  <SeparatorIcon className={config.separatorClass} />
                )}
                
                {/* Breadcrumb item */}
                {item.isEllipsis ? (
                  <span className={config.currentClass}>{item.label}</span>
                ) : item.href ? (
                  <Link 
                    href={item.href}
                    className={cn(
                      config.linkClass,
                      'flex items-center gap-1'
                    )}
                  >
                    {item.isHome && homeIcon && (
                      <HomeIcon className="w-4 h-4" />
                    )}
                    {item.label}
                  </Link>
                ) : (
                  <span 
                    className={config.currentClass}
                    aria-current="page"
                  >
                    {item.label}
                  </span>
                )}
              </li>
            ))}
          </ol>
        </div>
      </nav>

      {/* Structured Data for SEO */}
      {showStructuredData && breadcrumbs.length > 0 && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BreadcrumbList',
              itemListElement: breadcrumbs
                .filter(item => !item.isEllipsis && item.href !== null)
                .map((item, index) => ({
                  '@type': 'ListItem',
                  position: index + 1,
                  name: item.label,
                  item: item.href ? `${process.env.NEXT_PUBLIC_BASE_URL || 'https://bakasana-travel.blog'}${item.href}` : undefined
                }))
            })
          }}
        />
      )}
    </>
  );
};

// Specialized variants as named exports
export const MinimalBreadcrumbs = (props) => <Breadcrumbs variant="minimal" {...props} />;
export const ElegantBreadcrumbs = (props) => <Breadcrumbs variant="elegant" {...props} />;
export const ProfessionalBreadcrumbs = (props) => <Breadcrumbs variant="professional" {...props} />;

// Section-specific breadcrumbs
export const SectionBreadcrumbs = ({ section, items = [], className = '', ...props }) => {
  const sectionItems = [
    { label: 'Strona główna', href: '/' },
    { label: section, href: null },
    ...items
  ];
  
  return (
    <Breadcrumbs 
      customItems={sectionItems}
      className={className}
      {...props}
    />
  );
};

export default Breadcrumbs;