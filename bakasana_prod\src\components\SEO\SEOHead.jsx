/**
 * 🔍 BAKASANA - UNIFIED SEO HEAD COMPONENT
 * 
 * Skonsolidowany komponent SEO łączący funkcje z 11 poprzednich implementacji:
 * - Meta tags (title, description, keywords)
 * - Open Graph (Facebook, LinkedIn)
 * - Twitter Cards
 * - Canonical URLs
 * - Robots directives
 * - Language tags
 * - Viewport optimization
 * 
 * Usage:
 * <SEOHead
 *   title="Retreaty Jogi na Bali 2025"
 *   description="Odkryj transformacyjne retreaty jogi na Bali z certyfikowaną instruktorką"
 *   image="/images/og/bali-retreat.jpg"
 *   type="website"
 *   keywords={["joga", "bali", "retreat", "wellness"]}
 * />
 */

import Head from 'next/head';

const SEOHead = ({
  title,
  description,
  image,
  url,
  type = 'website',
  keywords = [],
  author = '<PERSON>',
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  locale = 'pl_PL',
  alternateLocales = [],
  canonical,
  robots = 'index,follow',
  viewport = 'width=device-width,initial-scale=1,viewport-fit=cover',
  themeColor = '#B8935C',
  noindex = false,
  nofollow = false,
  ...props
}) => {
  // Site configuration
  const siteName = 'Bakasana Studio';
  const siteDescription = 'Transformacyjne retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://bakasana-travel.blog';
  
  // Computed values
  const fullTitle = title ? `${title} | ${siteName}` : siteName;
  const finalDescription = description || siteDescription;
  const finalImage = image ? (image.startsWith('http') ? image : `${baseUrl}${image}`) : `${baseUrl}/images/og/default.jpg`;
  const finalUrl = url || canonical || baseUrl;
  const finalKeywords = keywords.length > 0 ? keywords.join(', ') : 'joga, bali, retreat, wellness, transformacja, podróże, sri lanka, instruktor jogi';
  
  // Robots directive
  const robotsContent = noindex || nofollow 
    ? `${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}`
    : robots;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      <meta name="author" content={author} />
      <meta name="robots" content={robotsContent} />
      <meta name="viewport" content={viewport} />
      <meta name="theme-color" content={themeColor} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalUrl} />
      
      {/* Language and Locale */}
      <meta httpEquiv="content-language" content="pl" />
      <meta property="og:locale" content={locale} />
      {alternateLocales.map(altLocale => (
        <meta key={altLocale} property="og:locale:alternate" content={altLocale} />
      ))}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={finalUrl} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={title || siteName} />
      <meta property="og:site_name" content={siteName} />
      
      {/* Article specific Open Graph */}
      {type === 'article' && (
        <>
          <meta property="article:author" content={author} />
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          {tags.map(tag => (
            <meta key={tag} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:url" content={finalUrl} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      <meta name="twitter:image:alt" content={title || siteName} />
      <meta name="twitter:creator" content="@bakasana_studio" />
      <meta name="twitter:site" content="@bakasana_studio" />
      
      {/* Additional Meta Tags for Better SEO */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
      
      {/* DNS Prefetch for better performance */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://www.google-analytics.com" />
      
      {/* Favicon and App Icons */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/manifest.json" />
      
      {/* Additional props */}
      {Object.entries(props).map(([key, value]) => (
        <meta key={key} name={key} content={value} />
      ))}
    </Head>
  );
};

export default SEOHead;