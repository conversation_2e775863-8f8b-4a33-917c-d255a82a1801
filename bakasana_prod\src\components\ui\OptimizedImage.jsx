/**
 * 🖼️ BAKASANA - UNIFIED OPTIMIZED IMAGE COMPONENT
 * 
 * Skonsolidowany komponent łączący najlepsze funkcje z 4 poprzednich implementacji:
 * - Progressive enhancement z blur placeholder
 * - Intersection Observer dla lazy loading
 * - <PERSON>bsługa błędów z fallback UI
 * - Wsparcie dla Next.js Image
 * - Accessibility features
 * - <PERSON><PERSON><PERSON><PERSON> warianty (Hero, Gallery, Profile, Blog, Thumbnail)
 * 
 * Usage:
 * <OptimizedImage
 *   src="/images/hero.webp"
 *   alt="Bali sunset yoga"
 *   width={800}
 *   height={600}
 *   priority={true}
 *   variant="hero"
 * />
 */

'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

// Default blur placeholder - ultra lightweight base64
const DEFAULT_BLUR_DATA_URL = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';

// Predefined variants for common use cases
const VARIANTS = {
  hero: {
    quality: 90,
    priority: true,
    sizes: '100vw',
    className: 'object-cover w-full h-full'
  },
  gallery: {
    quality: 85,
    priority: false,
    sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
    className: 'object-cover rectangular'
  },
  profile: {
    quality: 90,
    priority: false,
    sizes: '200px',
    className: 'object-cover rectangular'
  },
  blog: {
    quality: 85,
    priority: false,
    sizes: '(max-width: 768px) 100vw, 800px',
    className: 'object-cover rectangular'
  },
  thumbnail: {
    quality: 80,
    priority: false,
    sizes: '150px',
    className: 'object-cover rectangular'
  }
};

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  priority = false,
  quality = 85,
  placeholder = 'blur',
  blurDataURL,
  className = '',
  containerClassName = '',
  sizes = '100vw',
  fill = false,
  loading = 'lazy',
  variant,
  objectFit = 'cover',
  objectPosition = 'center',
  onLoad,
  onError,
  style,
  threshold = 0.1,
  rootMargin = '50px',
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState('');
  const imgRef = useRef(null);

  // Apply variant settings if provided
  const variantSettings = variant ? VARIANTS[variant] : {};
  const finalPriority = variantSettings.priority ?? priority;
  const finalQuality = variantSettings.quality ?? quality;
  const finalSizes = variantSettings.sizes ?? sizes;
  const finalClassName = cn(variantSettings.className, className);

  // Generate dynamic blur placeholder if not provided
  const generateBlurDataURL = useCallback((w = 40, h = 30) => {
    if (typeof window === 'undefined') return DEFAULT_BLUR_DATA_URL;
    
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = w;
      canvas.height = h;
      
      // Create gradient background matching theme
      const gradient = ctx.createLinearGradient(0, 0, w, h);
      gradient.addColorStop(0, 'rgba(253, 252, 248, 0.8)'); // whisper
      gradient.addColorStop(0.5, 'rgba(184, 147, 92, 0.1)'); // temple-gold
      gradient.addColorStop(1, 'rgba(253, 252, 248, 0.8)'); // whisper
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, w, h);
      
      return canvas.toDataURL('image/jpeg', 0.1);
    } catch {
      return DEFAULT_BLUR_DATA_URL;
    }
  }, []);

  const finalBlurDataURL = blurDataURL || generateBlurDataURL();

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (finalPriority) {
      setIsInView(true);
      return;
    }

    if (!imgRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(imgRef.current);

    return () => observer.disconnect();
  }, [finalPriority, threshold, rootMargin]);

  // Load image when in view or priority
  useEffect(() => {
    if ((finalPriority || isInView) && src && !currentSrc) {
      setCurrentSrc(src);
    }
  }, [isInView, finalPriority, src, currentSrc]);

  // Handle image load
  const handleLoad = useCallback((e) => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.(e);
  }, [onLoad]);

  // Handle image error
  const handleError = useCallback((e) => {
    setHasError(true);
    setIsLoaded(false);
    onError?.(e);
  }, [onError]);

  // Error fallback component
  if (hasError) {
    return (
      <div 
        ref={imgRef}
        className={cn(
          'flex items-center justify-center bg-whisper text-stone/60 text-sm',
          containerClassName
        )}
        style={fill ? { width: '100%', height: '100%', position: 'relative' } : { width, height, ...style }}
      >
        <div className="text-center">
          <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
          <p className="text-xs">Nie można załadować obrazu</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={imgRef}
      className={cn(
        'relative overflow-hidden bg-whisper',
        containerClassName
      )}
      style={fill ? { width: '100%', height: '100%', position: 'relative' } : { width, height, ...style }}
    >
      {/* Blur placeholder background */}
      {placeholder === 'blur' && !isLoaded && (
        <div
          className={cn(
            'absolute inset-0 z-0 bg-cover bg-center bg-no-repeat transition-opacity duration-300',
            isLoaded ? 'opacity-0' : 'opacity-100'
          )}
          style={{
            backgroundImage: `url(${finalBlurDataURL})`,
            filter: 'blur(20px)',
            transform: 'scale(1.1)',
          }}
        />
      )}
      
      {/* Shimmer loading animation */}
      {!isLoaded && !hasError && currentSrc && (
        <div className="absolute inset-0 z-10">
          <div className="w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
        </div>
      )}
      
      {/* Main image */}
      {currentSrc && (
        <Image
          src={currentSrc}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          fill={fill}
          priority={finalPriority}
          quality={finalQuality}
          placeholder={placeholder}
          blurDataURL={finalBlurDataURL}
          sizes={finalSizes}
          loading={finalPriority ? 'eager' : loading}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'transition-all duration-300 ease-out',
            isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-105',
            finalClassName
          )}
          style={{
            objectFit: fill ? objectFit : undefined,
            objectPosition: fill ? objectPosition : undefined,
          }}
          {...props}
        />
      )}
      
      {/* Loading indicator for slow connections */}
      {currentSrc && !isLoaded && !hasError && (
        <div className="absolute inset-0 z-30 flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-temple-gold border-t-transparent rectangular animate-spin" />
        </div>
      )}
    </div>
  );
};

// Specialized variants as named exports for backward compatibility
export const HeroImage = ({ src, alt, className = '', priority = true, ...props }) => (
  <OptimizedImage
    src={src}
    alt={alt}
    variant="hero"
    priority={priority}
    fill
    className={className}
    {...props}
  />
);

export const GalleryImage = ({ src, alt, className = '', width = 400, height = 300, ...props }) => (
  <OptimizedImage
    src={src}
    alt={alt}
    variant="gallery"
    width={width}
    height={height}
    className={className}
    {...props}
  />
);

export const ProfileImage = ({ src, alt, size = 200, className = '', ...props }) => (
  <OptimizedImage
    src={src}
    alt={alt}
    variant="profile"
    width={size}
    height={size}
    className={className}
    {...props}
  />
);

export const BlogImage = ({ src, alt, className = '', width = 800, height = 450, ...props }) => (
  <OptimizedImage
    src={src}
    alt={alt}
    variant="blog"
    width={width}
    height={height}
    className={className}
    {...props}
  />
);

export const ThumbnailImage = ({ src, alt, className = '', width = 150, height = 150, ...props }) => (
  <OptimizedImage
    src={src}
    alt={alt}
    variant="thumbnail"
    width={width}
    height={height}
    className={className}
    {...props}
  />
);

export default OptimizedImage;