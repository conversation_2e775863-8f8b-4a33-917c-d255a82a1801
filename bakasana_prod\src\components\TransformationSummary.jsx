'use client';

import React from 'react';
import { 
  SectionTitle, 
  BodyText, 
  CardTitle, 
  HandwritingText,
  WarmDivider 
} from '@/components/ui/UnifiedTypography';
import UnifiedCard from '@/components/ui/UnifiedCard';
import UnifiedButton from '@/components/ui/UnifiedButton';

/**
 * TransformationSummary - Component showing the complete BAKASANA transformation
 * From "exclusive luxury" to "warm elegance"
 */

const transformationAreas = [
  {
    area: "Typography",
    before: {
      title: "Zimna <PERSON>",
      features: [
        "Montserrat - bezpieczny, korporacyjny",
        "Ciasna inter-linia (1.3)",
        "Zimne pogrubienia",
        "Minimalne marginesy"
      ],
      feeling: "Podziwiam z daleka"
    },
    after: {
      title: "Ciepła Elegancja",
      features: [
        "Playfair Display - serif z charakterem",
        "Oddychająca inter-linia (1.8)",
        "Ciepłe podkreślenia rose gold",
        "<PERSON><PERSON><PERSON><PERSON><PERSON> przestrzeni na oddech"
      ],
      feeling: "Ch<PERSON>ę tam być"
    }
  },
  {
    area: "Kolory",
    before: {
      title: "<PERSON>łodna Paleta",
      features: [
        "Zimna czerń i szaro<PERSON>ci",
        "Ostre kontrasty",
        "Zimne złoto",
        "Brak ciepła"
      ],
      feeling: "Dystans i formalność"
    },
    after: {
      title: "Ciepła Harmonia",
      features: [
        "Ciepły charcoal i rose gold",
        "Brzoskwiniowe gradienty",
        "Naturalne przejścia",
        "Kolory świtu i zachodu"
      ],
      feeling: "Bliskość i autentyczność"
    }
  },
  {
    area: "Interakcje",
    before: {
      title: "Statyczne Elementy",
      features: [
        "Brak animacji",
        "Ostre przejścia",
        "Mechaniczne hover",
        "Brak życia"
      ],
      feeling: "Sztywność"
    },
    after: {
      title: "Oddychające Życie",
      features: [
        "Animacje jak oddech",
        "Płynne przejścia",
        "Organiczne ruchy",
        "Żywe elementy"
      ],
      feeling: "Naturalność"
    }
  },
  {
    area: "Fotografia",
    before: {
      title: "Perfekcyjne Pozy",
      features: [
        "Wyreżyserowane zdjęcia",
        "Zimne światło",
        "Idealne pozycje",
        "Brak emocji"
      ],
      feeling: "Nieosiągalne"
    },
    after: {
      title: "Autentyczne Momenty",
      features: [
        "Złapane chwile radości",
        "Złota godzina",
        "Prawdziwe emocje",
        "Detale i tekstury"
      ],
      feeling: "Osiągalne i inspirujące"
    }
  }
];

export default function TransformationSummary({ className = '', showDemo = true, ...props }) {
  return (
    <div className={`space-y-16 ${className}`} {...props}>
      {/* Header */}
      <div className="text-center">
        <SectionTitle breathing>
          Transformacja Kompletna
        </SectionTitle>
        <BodyText className="text-xl max-w-3xl mx-auto" breathing>
          Od <strong>'ekskluzywnego luksusu'</strong> do <HandwritingText className="text-2xl">'ciepłej elegancji'</HandwritingText>
        </BodyText>
      </div>

      {/* Transformation Areas */}
      <div className="space-y-12">
        {transformationAreas.map((area, index) => (
          <div key={area.area}>
            <CardTitle className="text-center mb-8" breathing>
              {area.area}
            </CardTitle>
            
            <div className="grid md:grid-cols-2 gap-8">
              {/* Before */}
              <UnifiedCard variant="minimal" padding="lg" className="border-l-4 border-stone-light">
                <div className="space-y-4">
                  <h4 className="font-playfair text-lg text-stone font-light">
                    Przed: {area.before.title}
                  </h4>
                  <ul className="space-y-2">
                    {area.before.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-stone">
                        <span className="text-stone-light mr-2 mt-1">•</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="pt-3 border-t border-stone-light">
                    <p className="text-xs text-stone italic">
                      Uczucie: {area.before.feeling}
                    </p>
                  </div>
                </div>
              </UnifiedCard>

              {/* After */}
              <UnifiedCard variant="breathing" padding="lg" className="border-l-4 border-rose-gold">
                <div className="space-y-4">
                  <h4 className="font-playfair text-lg text-charcoal">
                    Po: {area.after.title}
                  </h4>
                  <ul className="space-y-2">
                    {area.after.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-charcoal-light">
                        <span className="text-rose-gold mr-2 mt-1">•</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="pt-3 border-t border-rose-gold-light">
                    <p className="text-xs text-rose-gold-warm italic">
                      Uczucie: <span className="warm-underline">{area.after.feeling}</span>
                    </p>
                  </div>
                </div>
              </UnifiedCard>
            </div>
            
            {index < transformationAreas.length - 1 && <WarmDivider />}
          </div>
        ))}
      </div>

      {/* Key Principles */}
      <div className="bg-gradient-to-r from-dawn-cream to-dawn-peach p-12 text-center">
        <CardTitle className="mb-8">
          Kluczowe Zasady Transformacji
        </CardTitle>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="space-y-3">
            <div className="w-12 h-12 bg-rose-gold rounded-full mx-auto flex items-center justify-center breathe-glow">
              <span className="text-sanctuary text-xl">🌅</span>
            </div>
            <h5 className="font-playfair text-lg text-charcoal">Ciepło</h5>
            <p className="text-sm text-charcoal-light">
              Kolory świtu zamiast zimnych kontrastów
            </p>
          </div>
          
          <div className="space-y-3">
            <div className="w-12 h-12 bg-rose-gold rounded-full mx-auto flex items-center justify-center breathe-glow">
              <span className="text-sanctuary text-xl">💫</span>
            </div>
            <h5 className="font-playfair text-lg text-charcoal">Autentyczność</h5>
            <p className="text-sm text-charcoal-light">
              Prawdziwe momenty zamiast perfekcji
            </p>
          </div>
          
          <div className="space-y-3">
            <div className="w-12 h-12 bg-rose-gold rounded-full mx-auto flex items-center justify-center breathe-glow">
              <span className="text-sanctuary text-xl">🌸</span>
            </div>
            <h5 className="font-playfair text-lg text-charcoal">Oddech</h5>
            <p className="text-sm text-charcoal-light">
              Przestrzeń i ruch jak naturalny rytm
            </p>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="text-center space-y-8">
        <CardTitle breathing>
          Efekt Końcowy
        </CardTitle>
        
        <div className="max-w-4xl mx-auto">
          <BodyText className="text-lg mb-8" breathing>
            Strona, która <span className="warm-underline">zaprasza</span> zamiast dystansować. 
            Elegancja, która <HandwritingText>przytula</HandwritingText> zamiast onieśmielać.
          </BodyText>
          
          <div className="grid md:grid-cols-2 gap-8 text-left">
            <div>
              <h5 className="font-playfair text-lg mb-4 text-charcoal">Zachowane:</h5>
              <ul className="space-y-2 text-charcoal-light">
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">✓</span>
                  Elegancka estetyka
                </li>
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">✓</span>
                  Minimalistyczny design
                </li>
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">✓</span>
                  Profesjonalny wizerunek
                </li>
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">✓</span>
                  Wysoka jakość
                </li>
              </ul>
            </div>
            
            <div>
              <h5 className="font-playfair text-lg mb-4 text-charcoal">Dodane:</h5>
              <ul className="space-y-2 text-charcoal-light">
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">+</span>
                  Ciepło i bliskość
                </li>
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">+</span>
                  Autentyczność
                </li>
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">+</span>
                  Emocjonalne zaangażowanie
                </li>
                <li className="flex items-start">
                  <span className="text-rose-gold mr-2">+</span>
                  Oddychająca przestrzeń
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Demo CTA */}
      {showDemo && (
        <div className="text-center space-y-6">
          <WarmDivider />
          <div className="space-x-6">
            <UnifiedButton variant="primary" size="lg">
              Zobacz transformację
            </UnifiedButton>
            <UnifiedButton variant="breathing" size="lg">
              Poczuj różnicę
            </UnifiedButton>
          </div>
        </div>
      )}
    </div>
  );
}