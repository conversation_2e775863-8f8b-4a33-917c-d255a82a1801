'use client';

import { useEffect, useRef } from 'react';

/**
 * Focus Management for better accessibility
 * Handles focus trapping, restoration, and keyboard navigation
 */

export function useFocusTrap(isActive = false) {
  const containerRef = useRef(null);
  const previousFocusRef = useRef(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    // Store previous focus
    previousFocusRef.current = document.activeElement;

    // Focus first element
    if (firstElement) {
      firstElement.focus();
    }

    const handleTabKey = (e) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        // Custom escape handler can be passed as prop
        if (typeof window !== 'undefined' && window.closeFocusTrap) {
          window.closeFocusTrap();
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);
    document.addEventListener('keydown', handleEscapeKey);

    return () => {
      document.removeEventListener('keydown', handleTabKey);
      document.removeEventListener('keydown', handleEscapeKey);
      
      // Restore previous focus
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    };
  }, [isActive]);

  return containerRef;
}

export function useSkipLinks() {
  useEffect(() => {
    const skipLinks = document.querySelectorAll('.skip-link');
    
    skipLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const target = document.getElementById(targetId);
        
        if (target) {
          target.focus();
          target.scrollIntoView({ behavior: 'smooth', block: 'start' });
          
          // Announce to screen readers
          const announcement = document.createElement('div');
          announcement.setAttribute('aria-live', 'polite');
          announcement.setAttribute('aria-atomic', 'true');
          announcement.className = 'sr-only';
          announcement.textContent = `Przeszedłeś do sekcji: ${target.getAttribute('aria-label') || target.textContent}`;
          
          document.body.appendChild(announcement);
          setTimeout(() => document.body.removeChild(announcement), 1000);
        }
      });
    });
  }, []);
}

export function useAriaLiveRegion() {
  const regionRef = useRef(null);

  const announce = (message, priority = 'polite') => {
    if (!regionRef.current) return;

    regionRef.current.setAttribute('aria-live', priority);
    regionRef.current.textContent = message;

    // Clear after announcement
    setTimeout(() => {
      if (regionRef.current) {
        regionRef.current.textContent = '';
      }
    }, 1000);
  };

  const LiveRegion = () => (
    <div
      ref={regionRef}
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
    />
  );

  return { announce, LiveRegion };
}

// Screen reader only utility component
export function ScreenReaderOnly({ children, as: Component = 'span', ...props }) {
  return (
    <Component
      className="sr-only"
      {...props}
    >
      {children}
    </Component>
  );
}

// Focus visible utility for custom focus styles
export function useFocusVisible() {
  useEffect(() => {
    let hadKeyboardEvent = true;
    let keyboardThrottleTimeout = 0;

    const pointerEvent = () => {
      hadKeyboardEvent = false;
    };

    const keyboardEvent = (e) => {
      if (e.metaKey || e.altKey || e.ctrlKey) return;
      hadKeyboardEvent = true;
    };

    const focusEvent = (e) => {
      if (hadKeyboardEvent || e.target.matches(':focus-visible')) {
        e.target.classList.add('focus-visible');
      }
    };

    const blurEvent = (e) => {
      e.target.classList.remove('focus-visible');
    };

    document.addEventListener('keydown', keyboardEvent, true);
    document.addEventListener('mousedown', pointerEvent, true);
    document.addEventListener('pointerdown', pointerEvent, true);
    document.addEventListener('touchstart', pointerEvent, true);
    document.addEventListener('focus', focusEvent, true);
    document.addEventListener('blur', blurEvent, true);

    return () => {
      document.removeEventListener('keydown', keyboardEvent, true);
      document.removeEventListener('mousedown', pointerEvent, true);
      document.removeEventListener('pointerdown', pointerEvent, true);
      document.removeEventListener('touchstart', pointerEvent, true);
      document.removeEventListener('focus', focusEvent, true);
      document.removeEventListener('blur', blurEvent, true);
    };
  }, []);
}