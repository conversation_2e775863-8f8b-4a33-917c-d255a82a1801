'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { CheckCircleIcon, ExclamationCircleIcon, ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

// Toast Context
const ToastContext = createContext();

export const useUnifiedToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useUnifiedToast must be used within a ToastProvider');
  }
  return context;
};

// Toast Provider
export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = (toast) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = { ...toast, id };
    setToasts(prev => [...prev, newToast]);

    // Auto remove after duration
    setTimeout(() => {
      removeToast(id);
    }, toast.duration || 5000);

    return id;
  };

  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ addToast, removeToast, toasts }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
};

// Toast Container
const ToastContainer = () => {
  const { toasts, removeToast } = useUnifiedToast();

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map(toast => (
        <UnifiedToast
          key={toast.id}
          {...toast}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </div>
  );
};

// Base Message Component
const BaseMessage = ({ 
  type = 'info', 
  children, 
  className,
  icon: CustomIcon,
  onClose,
  ...props 
}) => {
  const icons = {
    success: CheckCircleIcon,
    error: ExclamationCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon,
  };

  const Icon = CustomIcon || icons[type];

  const variants = {
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800',
  };

  const iconVariants = {
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500',
  };

  return (
    <div
      className={cn(
        'flex items-start gap-3 p-4 rounded-lg border',
        variants[type],
        className
      )}
      {...props}
    >
      {Icon && (
        <Icon className={cn('w-5 h-5 mt-0.5 flex-shrink-0', iconVariants[type])} />
      )}
      <div className="flex-1 text-sm">
        {children}
      </div>
      {onClose && (
        <button
          onClick={onClose}
          className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <span className="sr-only">Zamknij</span>
          ×
        </button>
      )}
    </div>
  );
};

// Specific Message Components
export const SuccessMessage = ({ children, ...props }) => (
  <BaseMessage type="success" {...props}>
    {children}
  </BaseMessage>
);

export const ErrorMessage = ({ children, ...props }) => (
  <BaseMessage type="error" {...props}>
    {children}
  </BaseMessage>
);

export const WarningMessage = ({ children, ...props }) => (
  <BaseMessage type="warning" {...props}>
    {children}
  </BaseMessage>
);

export const InfoMessage = ({ children, ...props }) => (
  <BaseMessage type="info" {...props}>
    {children}
  </BaseMessage>
);

export const LoadingMessage = ({ children, className, ...props }) => (
  <div
    className={cn(
      'flex items-center gap-3 p-4 bg-gray-50 border border-gray-200 rounded-lg text-gray-700',
      className
    )}
    {...props}
  >
    <div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-gray-600"></div>
    <div className="text-sm">
      {children || 'Ładowanie...'}
    </div>
  </div>
);

// Toast Component
export const UnifiedToast = ({ 
  type = 'info', 
  title, 
  message, 
  onClose, 
  className,
  ...props 
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose?.();
    }, 300);
  };

  const variants = {
    success: 'bg-green-600 text-white',
    error: 'bg-red-600 text-white',
    warning: 'bg-yellow-600 text-white',
    info: 'bg-blue-600 text-white',
  };

  return (
    <div
      className={cn(
        'transform transition-all duration-300 ease-in-out',
        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0',
        'max-w-sm w-full shadow-lg rounded-lg pointer-events-auto',
        variants[type],
        className
      )}
      {...props}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-1">
            {title && (
              <p className="text-sm font-medium">
                {title}
              </p>
            )}
            {message && (
              <p className={cn('text-sm', title ? 'mt-1 opacity-90' : '')}>
                {message}
              </p>
            )}
          </div>
          <button
            onClick={handleClose}
            className="ml-4 flex-shrink-0 text-white/80 hover:text-white transition-colors"
          >
            <span className="sr-only">Zamknij</span>
            ×
          </button>
        </div>
      </div>
    </div>
  );
};

// Main UnifiedFeedback Component
const UnifiedFeedback = {
  Success: SuccessMessage,
  Error: ErrorMessage,
  Warning: WarningMessage,
  Info: InfoMessage,
  Loading: LoadingMessage,
  Toast: UnifiedToast,
  Provider: ToastProvider,
};

export default UnifiedFeedback;