#!/usr/bin/env node

/**
 * BAKASANA BUNDLE SIZE ANALYZER
 * Analyzes Next.js bundle size and identifies optimization opportunities
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 BAKASANA BUNDLE SIZE ANALYZER');
console.log('=====================================\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 1. Analyze package.json dependencies
function analyzeDependencies() {
  console.log(colorize('📦 DEPENDENCY ANALYSIS', 'cyan'));
  console.log('========================\n');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const deps = packageJson.dependencies || {};
    const devDeps = packageJson.devDependencies || {};
    
    console.log(colorize(`Production Dependencies: ${Object.keys(deps).length}`, 'green'));
    console.log(colorize(`Development Dependencies: ${Object.keys(devDeps).length}`, 'yellow'));
    
    // Heavy dependencies to watch
    const heavyDeps = [
      'next', 'react', 'react-dom', 'tailwindcss', 
      'framer-motion', 'three', 'gsap', 'lodash',
      'moment', 'date-fns', 'chart.js', 'recharts'
    ];
    
    console.log('\n' + colorize('🚨 HEAVY DEPENDENCIES FOUND:', 'red'));
    heavyDeps.forEach(dep => {
      if (deps[dep]) {
        console.log(`  ⚠️  ${dep}: ${deps[dep]}`);
      }
    });
    
    // Unused dependencies check
    console.log('\n' + colorize('🔍 POTENTIAL UNUSED DEPENDENCIES:', 'yellow'));
    const srcFiles = getAllJSFiles('./src');
    const unusedDeps = [];
    
    Object.keys(deps).forEach(dep => {
      const isUsed = srcFiles.some(file => {
        try {
          const content = fs.readFileSync(file, 'utf8');
          return content.includes(`from '${dep}'`) || 
                 content.includes(`require('${dep}')`) ||
                 content.includes(`import('${dep}')`);
        } catch (e) {
          return false;
        }
      });
      
      if (!isUsed && !['next', 'react', 'react-dom'].includes(dep)) {
        unusedDeps.push(dep);
      }
    });
    
    if (unusedDeps.length > 0) {
      unusedDeps.forEach(dep => {
        console.log(`  📦 ${dep} - potentially unused`);
      });
    } else {
      console.log('  ✅ No obviously unused dependencies found');
    }
    
  } catch (error) {
    console.log(colorize('❌ Error analyzing dependencies:', 'red'), error.message);
  }
}

// 2. Analyze source code files
function analyzeSourceFiles() {
  console.log('\n' + colorize('📁 SOURCE CODE ANALYSIS', 'cyan'));
  console.log('==========================\n');
  
  const srcPath = './src';
  const files = getAllJSFiles(srcPath);
  const fileSizes = [];
  
  files.forEach(file => {
    try {
      const stats = fs.statSync(file);
      fileSizes.push({
        path: file.replace('./src/', ''),
        size: stats.size,
        lines: fs.readFileSync(file, 'utf8').split('\n').length
      });
    } catch (e) {
      // Skip files that can't be read
    }
  });
  
  // Sort by size
  fileSizes.sort((a, b) => b.size - a.size);
  
  console.log(colorize('🔥 LARGEST SOURCE FILES:', 'red'));
  fileSizes.slice(0, 10).forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.path}`);
    console.log(`     Size: ${formatBytes(file.size)} | Lines: ${file.lines}`);
  });
  
  // Analyze by directory
  const dirSizes = {};
  fileSizes.forEach(file => {
    const dir = path.dirname(file.path);
    if (!dirSizes[dir]) {
      dirSizes[dir] = { size: 0, files: 0 };
    }
    dirSizes[dir].size += file.size;
    dirSizes[dir].files += 1;
  });
  
  console.log('\n' + colorize('📂 DIRECTORY SIZES:', 'blue'));
  Object.entries(dirSizes)
    .sort(([,a], [,b]) => b.size - a.size)
    .slice(0, 8)
    .forEach(([dir, stats]) => {
      console.log(`  ${dir}: ${formatBytes(stats.size)} (${stats.files} files)`);
    });
}

// 3. Analyze CSS files
function analyzeCSSFiles() {
  console.log('\n' + colorize('🎨 CSS ANALYSIS', 'cyan'));
  console.log('==================\n');
  
  const cssFiles = getAllCSSFiles('./src');
  const cssSizes = [];
  
  cssFiles.forEach(file => {
    try {
      const stats = fs.statSync(file);
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n').length;
      const rules = (content.match(/\{[^}]*\}/g) || []).length;
      
      cssSizes.push({
        path: file.replace('./src/', ''),
        size: stats.size,
        lines,
        rules
      });
    } catch (e) {
      // Skip files that can't be read
    }
  });
  
  cssSizes.sort((a, b) => b.size - a.size);
  
  console.log(colorize('🎨 CSS FILES BY SIZE:', 'magenta'));
  cssSizes.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.path}`);
    console.log(`     Size: ${formatBytes(file.size)} | Lines: ${file.lines} | Rules: ${file.rules}`);
  });
  
  const totalCSSSize = cssSizes.reduce((sum, file) => sum + file.size, 0);
  console.log(`\n  📊 Total CSS Size: ${colorize(formatBytes(totalCSSSize), 'bright')}`);
}

// 4. Check for optimization opportunities
function checkOptimizations() {
  console.log('\n' + colorize('⚡ OPTIMIZATION OPPORTUNITIES', 'cyan'));
  console.log('==================================\n');
  
  const opportunities = [];
  
  // Check for large images
  const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];
  const publicImages = getAllFiles('./public').filter(file => 
    imageExts.some(ext => file.toLowerCase().endsWith(ext))
  );
  
  const largeImages = [];
  publicImages.forEach(file => {
    try {
      const stats = fs.statSync(file);
      if (stats.size > 500 * 1024) { // > 500KB
        largeImages.push({
          path: file.replace('./public/', ''),
          size: stats.size
        });
      }
    } catch (e) {
      // Skip
    }
  });
  
  if (largeImages.length > 0) {
    opportunities.push({
      type: 'Images',
      priority: 'HIGH',
      description: `${largeImages.length} large images found (>500KB)`,
      action: 'Optimize with WebP/AVIF, proper sizing, compression'
    });
  }
  
  // Check for duplicate CSS
  const cssContent = getAllCSSFiles('./src').map(file => {
    try {
      return fs.readFileSync(file, 'utf8');
    } catch (e) {
      return '';
    }
  }).join('\n');
  
  const duplicateRules = findDuplicateCSS(cssContent);
  if (duplicateRules > 10) {
    opportunities.push({
      type: 'CSS',
      priority: 'MEDIUM',
      description: `~${duplicateRules} potentially duplicate CSS rules`,
      action: 'Consolidate duplicate styles, use CSS purging'
    });
  }
  
  // Check for unused imports
  const jsFiles = getAllJSFiles('./src');
  let unusedImports = 0;
  jsFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const imports = content.match(/import\s+.*?\s+from\s+['"][^'"]+['"]/g) || [];
      imports.forEach(imp => {
        const varName = imp.match(/import\s+(\w+)/)?.[1];
        if (varName && !content.includes(varName, imp.length)) {
          unusedImports++;
        }
      });
    } catch (e) {
      // Skip
    }
  });
  
  if (unusedImports > 5) {
    opportunities.push({
      type: 'JavaScript',
      priority: 'MEDIUM',
      description: `~${unusedImports} potentially unused imports`,
      action: 'Remove unused imports, implement tree shaking'
    });
  }
  
  // Display opportunities
  if (opportunities.length > 0) {
    opportunities.forEach((opp, index) => {
      const priorityColor = opp.priority === 'HIGH' ? 'red' : 
                           opp.priority === 'MEDIUM' ? 'yellow' : 'green';
      
      console.log(`${index + 1}. ${colorize(opp.type, 'bright')} - ${colorize(opp.priority, priorityColor)}`);
      console.log(`   ${opp.description}`);
      console.log(`   💡 ${opp.action}\n`);
    });
  } else {
    console.log(colorize('✅ No major optimization opportunities found!', 'green'));
  }
}

// Helper functions
function getAllJSFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory() && !item.name.startsWith('.')) {
      files.push(...getAllJSFiles(fullPath));
    } else if (item.isFile() && /\.(js|jsx|ts|tsx)$/.test(item.name)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function getAllCSSFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory() && !item.name.startsWith('.')) {
      files.push(...getAllCSSFiles(fullPath));
    } else if (item.isFile() && /\.css$/.test(item.name)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function getAllFiles(dir) {
  const files = [];
  try {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name);
      if (item.isDirectory() && !item.name.startsWith('.')) {
        files.push(...getAllFiles(fullPath));
      } else if (item.isFile()) {
        files.push(fullPath);
      }
    }
  } catch (e) {
    // Directory doesn't exist or can't be read
  }
  
  return files;
}

function findDuplicateCSS(content) {
  const rules = content.match(/[^{}]+\{[^{}]*\}/g) || [];
  const ruleMap = {};
  let duplicates = 0;
  
  rules.forEach(rule => {
    const normalized = rule.replace(/\s+/g, ' ').trim();
    if (ruleMap[normalized]) {
      duplicates++;
    } else {
      ruleMap[normalized] = true;
    }
  });
  
  return duplicates;
}

// Run analysis
async function runAnalysis() {
  try {
    analyzeDependencies();
    analyzeSourceFiles();
    analyzeCSSFiles();
    checkOptimizations();
    
    console.log('\n' + colorize('🎯 SUMMARY', 'cyan'));
    console.log('============');
    console.log('Analysis complete! Review the findings above for optimization opportunities.');
    console.log('\n' + colorize('💡 NEXT STEPS:', 'bright'));
    console.log('1. Implement dynamic imports for heavy components');
    console.log('2. Optimize images with WebP/AVIF formats');
    console.log('3. Remove unused dependencies and imports');
    console.log('4. Implement CSS purging for production builds');
    console.log('5. Consider code splitting for large pages\n');
    
  } catch (error) {
    console.log(colorize('❌ Analysis failed:', 'red'), error.message);
  }
}

// Run if called directly
if (require.main === module) {
  runAnalysis();
}

module.exports = { runAnalysis };
