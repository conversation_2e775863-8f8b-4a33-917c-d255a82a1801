'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { mainNavItems } from '@/data/navigationLinks';

export default function TestNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex items-center justify-between h-20">
          <Link href="/" className="font-bold text-2xl">
            BAKASANA
          </Link>
          
          <div className="hidden lg:flex items-center space-x-8">
            <Link href="/retreaty" className="text-gray-700 hover:text-gray-900">
              Retreaty
            </Link>
            <Link href="/kontakt" className="text-gray-700 hover:text-gray-900">
              Kontakt
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
}
