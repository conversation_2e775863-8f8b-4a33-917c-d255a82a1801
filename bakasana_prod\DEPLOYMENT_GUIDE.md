# 🚀 BAKASANA - Przewodnik Wdrożenia do Produkcji

## 📋 Lista kontrolna przed wdrożeniem

### 1. Konfiguracja środowiska produkcyjnego

#### A. Plik `.env.production`
- [ ] Z<PERSON><PERSON><PERSON><PERSON>j `NEXT_PUBLIC_GA_ID` - Google Analytics
- [ ] Zak<PERSON><PERSON>zuj `NEXT_PUBLIC_GTM_ID` - Google Tag Manager  
- [ ] Zaktualizuj `NEXT_PUBLIC_GOOGLE_VERIFICATION` - Google Search Console
- [ ] Zaktualizuj `NEXT_PUBLIC_SANITY_PROJECT_ID` - Sanity CMS
- [ ] Zak<PERSON>alizuj `SANITY_API_TOKEN` - Sanity API Token
- [ ] Z<PERSON>ń `ADMIN_PASSWORD` - silne hasło do panelu admin
- [ ] Zmień `JWT_SECRET` - losowy klucz JWT
- [ ] Zaktualizuj `RESEND_API_KEY` - klucz API do wysyłania emaili
- [ ] Zaktualizuj `NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY` - formularz kontaktowy

#### B. Weryfika<PERSON><PERSON> domeny
- [ ] Skonfiguruj domenę `bakasana-travel.blog`
- [ ] Skonfiguruj SSL/TLS certyfikat
- [ ] Zweryfikuj DNS settings

### 2. Sanity CMS Setup

```bash
# 1. Zaloguj się do Sanity
npx sanity login

# 2. Stwórz nowy projekt lub użyj istniejącego
npx sanity init

# 3. Wdróż Sanity Studio
npm run sanity:deploy
```

### 3. Vercel Deployment (Rekomendowane)

#### A. Przygotowanie
```bash
# 1. Zainstaluj Vercel CLI
npm i -g vercel

# 2. Zaloguj się
vercel login

# 3. Wdróż projekt
vercel --prod
```

#### B. Konfiguracja zmiennych środowiskowych w Vercel
1. Idź do Vercel Dashboard → Twój projekt → Settings → Environment Variables
2. Dodaj wszystkie zmienne z `.env.production`
3. Ustaw `NODE_ENV=production`

### 4. Alternatywne opcje wdrożenia

#### A. Netlify
```bash
# 1. Build projekt
npm run build

# 2. Wdróż folder out/
# Skonfiguruj zmienne środowiskowe w Netlify Dashboard
```

#### B. Docker (dla VPS/własnego serwera)
```bash
# 1. Stwórz Dockerfile (już istnieje)
# 2. Build image
docker build -t bakasana-travel .

# 3. Uruchom kontener
docker run -p 3000:3000 bakasana-travel
```

### 5. Konfiguracja usług zewnętrznych

#### A. Google Analytics & Search Console
1. Stwórz konto GA4: https://analytics.google.com
2. Skopiuj Measurement ID do `NEXT_PUBLIC_GA_ID`
3. Zweryfikuj stronę w Google Search Console
4. Skopiuj kod weryfikacyjny do `NEXT_PUBLIC_GOOGLE_VERIFICATION`

#### B. Email Service (Resend)
1. Stwórz konto: https://resend.com
2. Wygeneruj API key
3. Dodaj do `RESEND_API_KEY`

#### C. Formularz kontaktowy (Web3Forms)
1. Stwórz konto: https://web3forms.com
2. Stwórz nowy formularz
3. Skopiuj Access Key do `NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY`

### 6. Optymalizacje produkcyjne

#### A. Performance
- [x] Image optimization (Next.js Image)
- [x] Bundle splitting
- [x] Compression (gzip)
- [x] Caching headers
- [x] Critical CSS inlining

#### B. SEO
- [x] Sitemap generation
- [x] Robots.txt
- [x] Meta tags
- [x] Structured data
- [x] Open Graph images

#### C. Security
- [x] Security headers
- [x] CSP (Content Security Policy)
- [x] HTTPS redirect
- [x] XSS protection

### 7. Monitoring i Analytics

#### A. Core Web Vitals
- [x] Vercel Analytics integration
- [x] Performance monitoring
- [x] Real User Monitoring (RUM)

#### B. Error Tracking (Opcjonalne)
```bash
# Sentry setup
npm install @sentry/nextjs
```

### 8. Backup i Recovery

#### A. Sanity CMS Backup
```bash
# Export danych
npx sanity dataset export production backup.tar.gz
```

#### B. Code Repository
- [x] Git repository
- [ ] Automated backups
- [ ] Version tagging

## 🔧 Komendy produkcyjne

```bash
# Build produkcyjny
npm run build

# Start serwera produkcyjnego
npm run start

# Analiza bundle'a
npm run build:analyze

# Optymalizacja obrazów
npm run optimize-images

# Czyszczenie cache
npm run clean

# Test cross-browser
npm run test:browsers
```

## 📊 Metryki wydajności (cele)

- **Lighthouse Score**: 95+ (wszystkie kategorie)
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

## 🚨 Troubleshooting

### Problem: Build fails
```bash
# Wyczyść cache i node_modules
rm -rf .next node_modules
npm install
npm run build
```

### Problem: Images nie ładują się
- Sprawdź konfigurację `next.config.js`
- Zweryfikuj `remotePatterns` dla zewnętrznych obrazów

### Problem: Sanity nie działa
- Sprawdź `NEXT_PUBLIC_SANITY_PROJECT_ID`
- Zweryfikuj `SANITY_API_TOKEN`
- Sprawdź CORS settings w Sanity

## 📞 Wsparcie

W przypadku problemów:
1. Sprawdź logi buildów w Vercel/Netlify
2. Sprawdź browser console na błędy
3. Zweryfikuj wszystkie zmienne środowiskowe

---

**Powodzenia z wdrożeniem! 🚀**