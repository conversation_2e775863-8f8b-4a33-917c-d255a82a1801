/**
 * 🌟 BAKASANA - UNIFIED HERO COMPONENT
 * 
 * Skonsolidowany komponent <PERSON> ł<PERSON>ą<PERSON> 8 poprzednich implementacji:
 * - Variant: bakasana (główny hero z animacjami)
 * - Variant: minimal (minimalistyczny design)
 * - Variant: professional (profesjonalny wygląd)
 * - Variant: elegant (elegancki z gradientami)
 * - Variant: simple (prosty bez animacji)
 * - Variant: spectacular (z efektami wizualnymi)
 * 
 * Usage:
 * <Hero
 *   variant="bakasana"
 *   title="Transformacyjne Retreaty Jogi"
 *   subtitle="Odkryj siebie na Bali i Sri Lanka"
 *   image="/images/hero/bali-sunset.webp"
 *   ctaText="Odkryj Retreaty"
 *   ctaLink="/retreaty"
 * />
 */

'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import OptimizedImage from './OptimizedImage';

// Variant configurations
const VARIANTS = {
  bakasana: {
    containerClass: 'relative min-h-screen flex items-center justify-center overflow-hidden',
    contentClass: 'relative z-20 text-center max-w-6xl mx-auto px-6 lg:px-8',
    titleClass: 'text-5xl md:text-7xl lg:text-8xl font-primary font-light text-whisper mb-6 leading-[0.9] tracking-tight',
    subtitleClass: 'text-xl md:text-2xl font-secondary text-whisper/90 mb-12 max-w-3xl mx-auto leading-relaxed',
    ctaClass: 'inline-flex items-center gap-3 px-12 py-6 bg-temple-gold hover:bg-temple-gold/90 text-charcoal font-secondary font-medium text-lg tracking-wide transition-all duration-300 hover:scale-105 hover:shadow-2xl',
    hasOverlay: true,
    hasAnimations: true,
    hasParallax: true
  },
  minimal: {
    containerClass: 'relative min-h-[80vh] flex items-center justify-center',
    contentClass: 'text-center max-w-4xl mx-auto px-6',
    titleClass: 'text-4xl md:text-6xl font-primary font-light text-charcoal mb-6 leading-tight',
    subtitleClass: 'text-lg md:text-xl text-stone mb-8 max-w-2xl mx-auto',
    ctaClass: 'inline-flex items-center px-8 py-4 bg-temple-gold hover:bg-temple-gold/90 text-charcoal font-medium transition-colors duration-200',
    hasOverlay: false,
    hasAnimations: false,
    hasParallax: false
  },
  professional: {
    containerClass: 'relative min-h-screen flex items-center justify-center bg-gradient-to-br from-whisper to-rice',
    contentClass: 'text-center max-w-5xl mx-auto px-6 lg:px-8',
    titleClass: 'text-5xl md:text-7xl font-primary font-light text-charcoal mb-8 leading-tight',
    subtitleClass: 'text-xl md:text-2xl text-stone mb-10 max-w-3xl mx-auto leading-relaxed',
    ctaClass: 'inline-flex items-center gap-2 px-10 py-5 bg-charcoal hover:bg-charcoal/90 text-whisper font-medium text-lg transition-all duration-300 hover:shadow-lg',
    hasOverlay: false,
    hasAnimations: true,
    hasParallax: false
  },
  elegant: {
    containerClass: 'relative min-h-screen flex items-center justify-center overflow-hidden',
    contentClass: 'relative z-20 text-center max-w-6xl mx-auto px-6 lg:px-8',
    titleClass: 'text-6xl md:text-8xl font-primary font-extralight text-whisper mb-8 leading-[0.85] tracking-tight',
    subtitleClass: 'text-xl md:text-2xl font-secondary text-whisper/80 mb-12 max-w-3xl mx-auto italic',
    ctaClass: 'inline-flex items-center gap-3 px-12 py-6 bg-gradient-to-r from-temple-gold to-temple-gold/80 hover:from-temple-gold/90 hover:to-temple-gold/70 text-charcoal font-medium text-lg transition-all duration-500 hover:scale-105',
    hasOverlay: true,
    hasAnimations: true,
    hasParallax: true
  },
  simple: {
    containerClass: 'relative min-h-[70vh] flex items-center justify-center bg-whisper',
    contentClass: 'text-center max-w-3xl mx-auto px-6',
    titleClass: 'text-3xl md:text-5xl font-primary text-charcoal mb-6',
    subtitleClass: 'text-lg text-stone mb-8',
    ctaClass: 'inline-block px-8 py-3 bg-temple-gold text-charcoal font-medium hover:bg-temple-gold/90 transition-colors',
    hasOverlay: false,
    hasAnimations: false,
    hasParallax: false
  },
  spectacular: {
    containerClass: 'relative min-h-screen flex items-center justify-center overflow-hidden',
    contentClass: 'relative z-30 text-center max-w-7xl mx-auto px-6 lg:px-8',
    titleClass: 'text-6xl md:text-9xl font-primary font-thin text-whisper mb-10 leading-[0.8] tracking-tighter',
    subtitleClass: 'text-2xl md:text-3xl font-secondary text-whisper/90 mb-16 max-w-4xl mx-auto leading-relaxed',
    ctaClass: 'inline-flex items-center gap-4 px-16 py-8 bg-temple-gold hover:bg-temple-gold/90 text-charcoal font-bold text-xl tracking-wide transition-all duration-500 hover:scale-110 hover:shadow-2xl transform-gpu',
    hasOverlay: true,
    hasAnimations: true,
    hasParallax: true
  },
  oldmoney: {
    containerClass: 'relative min-h-screen flex items-center justify-center overflow-hidden bg-sanctuary',
    contentClass: 'relative z-30 text-center max-w-6xl mx-auto px-8 lg:px-12',
    titleClass: 'text-5xl md:text-7xl lg:text-8xl font-playfair font-light text-charcoal mb-12 leading-tight tracking-wide',
    subtitleClass: 'text-xl md:text-2xl font-crimson text-charcoal-light mb-16 max-w-4xl mx-auto leading-normal font-light',
    ctaClass: 'group inline-flex items-center gap-3 px-12 py-5 border border-enterprise-brown text-enterprise-brown hover:bg-enterprise-brown hover:text-sanctuary font-crimson font-medium text-lg tracking-wide transition-all duration-500 hover:scale-[1.02] hover:shadow-elegant',
    hasOverlay: false,
    hasAnimations: true,
    hasParallax: false
  }
};

const Hero = ({
  variant = 'bakasana',
  title = 'Transformacyjne Retreaty Jogi',
  subtitle = 'Odkryj siebie na magicznych wyspach Bali i Sri Lanka z certyfikowaną instruktorką',
  image = '/images/background/bali-hero.webp',
  ctaText = 'Odkryj Retreaty',
  ctaLink = '/retreaty',
  secondaryCtaText,
  secondaryCtaLink,
  badge,
  className = '',
  children,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const config = VARIANTS[variant] || VARIANTS.bakasana;

  useEffect(() => {
    if (config.hasAnimations) {
      const timer = setTimeout(() => setIsLoaded(true), 100);
      return () => clearTimeout(timer);
    } else {
      setIsLoaded(true);
    }
  }, [config.hasAnimations]);

  return (
    <section className={cn(config.containerClass, className)} {...props}>
      {/* Background Image */}
      {image && (
        <div className="absolute inset-0 z-0">
          <OptimizedImage
            src={image}
            alt={title}
            fill
            priority
            variant="hero"
            className={cn(
              'object-cover object-center',
              config.hasParallax && 'scale-105 transition-transform duration-[10s] ease-out'
            )}
          />
          
          {/* Overlay */}
          {config.hasOverlay && (
            <>
              <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40" />
              <div className="absolute inset-0 bg-sanctuary/10" />
            </>
          )}
        </div>
      )}

      {/* Main Content */}
      <div className={cn(
        config.contentClass,
        config.hasAnimations && 'transition-all duration-1000',
        config.hasAnimations && (isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8')
      )}>
        <div className="space-y-8">
          
          {/* Badge */}
          {badge && (
            <div className={cn(
              'inline-flex items-center gap-3 px-8 py-4 bg-sanctuary/90 backdrop-blur-sm border border-temple-gold/20',
              config.hasAnimations && 'transition-all duration-700 delay-200',
              config.hasAnimations && (isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4')
            )}>
              <span className="w-2 h-2 bg-temple-gold rectangular animate-pulse"></span>
              <span className="text-sm font-secondary text-charcoal tracking-[0.2em] uppercase font-medium">
                {badge}
              </span>
            </div>
          )}

          {/* Title */}
          <h1 className={cn(
            config.titleClass,
            config.hasAnimations && 'transition-all duration-1000 delay-300',
            config.hasAnimations && (isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8')
          )}>
            {title}
          </h1>

          {/* Subtitle */}
          {subtitle && (
            <p className={cn(
              config.subtitleClass,
              config.hasAnimations && 'transition-all duration-1000 delay-500',
              config.hasAnimations && (isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8')
            )}>
              {subtitle}
            </p>
          )}

          {/* CTA Buttons */}
          <div className={cn(
            'flex flex-col sm:flex-row gap-6 justify-center items-center',
            config.hasAnimations && 'transition-all duration-1000 delay-700',
            config.hasAnimations && (isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8')
          )}>
            {ctaText && ctaLink && (
              <Link href={ctaLink} className={config.ctaClass}>
                {ctaText}
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            )}
            
            {secondaryCtaText && secondaryCtaLink && (
              <Link 
                href={secondaryCtaLink} 
                className="inline-flex items-center gap-2 px-8 py-4 border-2 border-whisper/30 text-whisper hover:bg-whisper/10 font-medium transition-all duration-300"
              >
                {secondaryCtaText}
              </Link>
            )}
          </div>

          {/* Custom children */}
          {children && (
            <div className={cn(
              config.hasAnimations && 'transition-all duration-1000 delay-900',
              config.hasAnimations && (isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8')
            )}>
              {children}
            </div>
          )}
        </div>
      </div>

      {/* Scroll Indicator */}
      {variant === 'bakasana' || variant === 'spectacular' && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex flex-col items-center gap-2 text-whisper/60">
            <span className="text-sm font-secondary tracking-wider">SCROLL</span>
            <div className="w-px h-12 bg-gradient-to-b from-whisper/60 to-transparent"></div>
          </div>
        </div>
      )}
    </section>
  );
};

// Specialized variants as named exports for backward compatibility
export const BakasanaHero = (props) => <Hero variant="bakasana" {...props} />;
export const MinimalHero = (props) => <Hero variant="minimal" {...props} />;
export const ProfessionalHero = (props) => <Hero variant="professional" {...props} />;
export const ElegantHero = (props) => <Hero variant="elegant" {...props} />;
export const SimpleHero = (props) => <Hero variant="simple" {...props} />;
export const SpectacularHero = (props) => <Hero variant="spectacular" {...props} />;
export const OldMoneyHero = (props) => <Hero variant="oldmoney" {...props} />;

export default Hero;